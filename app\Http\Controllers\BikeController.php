<?php

namespace App\Http\Controllers;

use App\Models\Bike;
use Illuminate\Http\Request;

class BikeController extends Controller
{
    /**
     * Display bike detail page
     */
    public function show($slug)
    {
        $bike = Bike::where('slug', $slug)
            ->with(['colors', 'variants', 'specifications'])
            ->firstOrFail();

        // Get related bikes from same brand
        $relatedBikes = Bike::where('brand_name', $bike->brand_name)
            ->where('id', '!=', $bike->id)
            ->limit(4)
            ->get();

        return view('bikes.show', compact('bike', 'relatedBikes'));
    }

    /**
     * Get bike data for AJAX requests
     */
    public function getBikeData($id)
    {
        $bike = Bike::with(['colors', 'variants', 'specifications'])
            ->findOrFail($id);

        return response()->json([
            'bike' => $bike,
            'success' => true
        ]);
    }

    /**
     * Get bikes by brand for navigation
     */
    public function getByBrand($brand)
    {
        $bikes = Bike::where('brand_name', $brand)
            ->with(['colors'])
            ->get()
            ->groupBy('category');

        return response()->json([
            'bikes' => $bikes,
            'brand' => $brand,
            'success' => true
        ]);
    }
}
