{{-- 
EXAMPLE BLADE TEMPLATE FOR BIKE CAROUSEL SECTION
Copy this structure and adapt it to your Laravel project
--}}

@extends('layouts.app')

@section('title', 'Bajaj Motors - Bikes')

@section('content')
<!-- Bike Carousel Section -->
<section class="py-16 bg-gray-50 relative overflow-hidden">
  <div class="container mx-auto px-4">
    <!-- Background Brand Text -->
    <div class="absolute inset-0 flex items-center justify-center pointer-events-none">
      <h1
        class="text-[20vw] lg:text-[25vw] font-black text-indigo-100 select-none"
        id="background-brand-text"
      >
        {{ array_keys($bikeData)[0] ?? 'PULSAR' }}
      </h1>
    </div>

    <!-- Content -->
    <div class="relative z-10">
      <!-- Header -->
      <div class="text-center mb-12">
        <h2 class="text-3xl lg:text-5xl font-bold text-gray-900 mb-4" id="bike-title">
          {{ $bikeData[array_keys($bikeData)[0]]['bikes'][0]['name'] ?? 'PULSAR 220F ABS' }}
        </h2>
        <p
          class="text-gray-600 text-base lg:text-lg max-w-3xl mx-auto leading-relaxed"
          id="bike-description"
        >
          {{ $bikeData[array_keys($bikeData)[0]]['bikes'][0]['description'] ?? 'Experience the perfect blend of power and style...' }}
        </p>
      </div>

      <!-- Main Carousel Grid -->
      <div class="grid grid-cols-12 gap-8 items-center">
        <!-- Left Side - Navigation Arrow -->
        <div class="col-span-2 flex justify-center items-center">
          <button
            class="bike-prev-btn w-12 h-12 lg:w-14 lg:h-14 rounded-full border border-gray-300 flex items-center justify-center hover:border-gray-500 hover:bg-gray-50 transition-all duration-200"
            id="bike-prev-btn"
          >
            <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
          </button>
        </div>

        <!-- Center - Bike Display -->
        <div class="col-span-8">
          <div class="text-center">
            <!-- Bike Image -->
            <div class="mb-8">
              <img
                src="{{ $bikeData[array_keys($bikeData)[0]]['bikes'][0]['image'] ?? './assets/bikes/pulsar/pulsar_220f_abs.png' }}"
                alt="{{ $bikeData[array_keys($bikeData)[0]]['bikes'][0]['name'] ?? 'Pulsar 220F ABS' }}"
                class="w-full h-auto max-h-[400px] lg:max-h-[500px] object-contain"
                id="main-bike-image"
              />
            </div>

            <!-- Brand Info -->
            <div class="flex items-center justify-center space-x-6 mb-6">
              <img
                src="{{ $bikeData[array_keys($bikeData)[0]]['logo'] ?? './assets/brand-logos/pulsar-logo.png' }}"
                alt="{{ array_keys($bikeData)[0] ?? 'Pulsar' }} Logo"
                class="h-8"
                id="brand-logo"
              />
              <div class="flex items-center space-x-2">
                <img
                  src="{{ $bikeData[array_keys($bikeData)[0]]['categoryIcon'] ?? './assets/icons/sports.png' }}"
                  alt="{{ $bikeData[array_keys($bikeData)[0]]['category'] ?? 'Sports' }} Category"
                  class="h-6"
                  id="category-icon"
                />
                <span class="text-gray-600" id="category-text">
                  {{ $bikeData[array_keys($bikeData)[0]]['category'] ?? 'Sports' }}
                </span>
              </div>
            </div>

            <!-- Color Selection -->
            <div class="flex justify-center space-x-4 mb-6" id="color-selection">
              @if(isset($bikeData[array_keys($bikeData)[0]]['bikes'][0]['colors']))
                @foreach($bikeData[array_keys($bikeData)[0]]['bikes'][0]['colors'] as $index => $color)
                  <button
                    class="color-btn w-8 h-8 rounded-full border-2 border-gray-300 {{ $index === 0 ? 'active' : '' }}"
                    style="background-color: {{ $color['color'] }}"
                    data-color="{{ $color['name'] }}"
                  ></button>
                @endforeach
              @endif
            </div>

            <!-- Brand Tabs -->
            <div class="flex justify-center space-x-8 mb-8">
              @foreach($bikeData as $brandName => $brandInfo)
                <button 
                  class="brand-tab text-sm font-medium pb-2 border-b-2 {{ $loop->first ? 'border-black text-gray-800' : 'border-transparent text-gray-400' }} hover:text-gray-600 transition-colors duration-200"
                  data-brand="{{ $brandName }}"
                >
                  {{ $brandName }}
                </button>
              @endforeach
            </div>

            <!-- Model Variants -->
            @foreach($bikeData as $brandName => $brandInfo)
              <div class="model-group {{ strtolower($brandName) }}-models {{ $loop->first ? '' : 'hidden' }}">
                <div class="flex flex-wrap gap-4 justify-center">
                  @foreach($brandInfo['bikes'] as $index => $bike)
                    <button 
                      class="variant-btn px-4 py-2 text-sm border {{ $loop->parent->first && $index === 0 ? 'border-black text-gray-700' : 'border-transparent text-gray-500' }} hover:text-gray-700 transition-colors duration-200"
                      data-model="{{ $bike['id'] }}"
                    >
                      {{ $bike['name'] }}
                    </button>
                  @endforeach
                </div>
              </div>
            @endforeach
          </div>
        </div>

        <!-- Right Side - Navigation Arrow -->
        <div class="col-span-2 flex justify-center items-center">
          <button
            class="bike-next-btn w-12 h-12 lg:w-14 lg:h-14 rounded-full border border-gray-300 flex items-center justify-center hover:border-gray-500 hover:bg-gray-50 transition-all duration-200"
            id="bike-next-btn"
          >
            <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</section>
@endsection

@section('scripts')
{{-- Pass bike data to JavaScript --}}
<script>
    // Make bike data available globally for the carousel
    window.bikeData = @json($bikeData);
    
    console.log('✅ Bike data loaded from Laravel');
    console.log('📊 Available brands:', Object.keys(window.bikeData));
</script>

{{-- Include the carousel JavaScript --}}
<script src="{{ asset('js/bike-carousel-laravel.js') }}"></script>
@endsection

{{--
USAGE INSTRUCTIONS:

1. Place this file in: resources/views/bikes/index.blade.php
2. Copy js/bike-carousel-laravel.js to: public/js/bike-carousel-laravel.js
3. In your controller, structure data exactly as shown in LARAVEL_INTEGRATION_GUIDE.md
4. Make sure your layout has @yield('scripts') before closing </body> tag

CONTROLLER EXAMPLE:
public function index()
{
    $bikeData = [
        'PULSAR' => [
            'logo' => asset('assets/brand-logos/pulsar-logo.png'),
            'category' => 'Sports',
            'categoryIcon' => asset('assets/icons/sports.png'),
            'bikes' => [
                [
                    'id' => 'pulsar-220f-abs',
                    'name' => 'PULSAR 220F ABS',
                    'image' => asset('assets/bikes/pulsar/pulsar_220f_abs.png'),
                    'description' => 'Experience the perfect blend...',
                    'colors' => [
                        ['name' => 'black', 'color' => '#000000'],
                        ['name' => 'yellow', 'color' => '#facc15'],
                    ]
                ],
            ]
        ],
        // Add more brands...
    ];
    
    return view('bikes.index', compact('bikeData'));
}
--}}
