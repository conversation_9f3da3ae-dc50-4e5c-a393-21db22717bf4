<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Bajaj Motors</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto&display=swap" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b',
                        accent: '#f59e0b',
                        success: '#10b981',
                        failure: '#ef4444',
                    },
                    fontFamily: {
                        roboto: ['Roboto', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    <link rel="stylesheet" href="css/styles.css" />
    <style>
        .pulsar-text {
            font-size: 20vw;
        }

        @media (min-width: 1024px) {
            .pulsar-text {
                font-size: 25vw;
            }
        }

        .other-brand-text {
            font-size: 15vw;
        }

        @media (min-width: 1024px) {
            .other-brand-text {
                font-size: 18vw;
            }
        }


        .featured-text-column {
            grid-column: 1 / span 3;
        }

        .featured-image-column {
            grid-column: 4 / span 7;
        }

        .featured-text-column,
        .featured-image-column {
            transition: transform 0.3s ease;
        }

        .featured-text-column:hover,
        .featured-image-column:hover {
            transform: translateY(-5px);
        }

        @media (max-width: 1024px) {

            .featured-text-column,
            .featured-image-column {
                grid-column: 1 / -1;
            }
    </style>
</head>

<body class="bg-gray-50">
    <!-- Header -->
    <header class="header-overlay fixed top-0 left-0 right-0 z-50">
        <!-- Top Bar -->
        <div class="bg-transparent py-2 top-bar">
            <div class="top-bar-content">
                <div class="top-bar-left">
                    <img src="assets/golcha-logo.png" alt="golcha_logo" class="top-bar-logo" />
                    <span class="top-bar-text">GOLCHHA GROUP WITH LEGACY OF 100 YEAR</span>
                </div>
                <div class="top-bar-right">
                    <svg class="top-bar-icon" viewBox="0 0 23 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M6.89761 15.1618C8.28247 16.3099 10.0607 17 12.0001 17C16.4184 17 20.0001 13.4183 20.0001 9C20.0001 8.43095 19.9407 7.87578 19.8278 7.34036M6.89761 15.1618C5.12756 13.6944 4.00014 11.4789 4.00014 9C4.00014 4.58172 7.58186 1 12.0001 1C15.8494 1 19.0637 3.71853 19.8278 7.34036M6.89761 15.1618C8.85314 14.7147 11.1796 13.7828 13.526 12.4281C16.2564 10.8517 18.4773 9.01248 19.8278 7.34036M6.89761 15.1618C4.46844 15.7171 2.61159 15.5243 1.99965 14.4644C1.36934 13.3726 2.19631 11.5969 3.99999 9.70898M19.8278 7.34036C21.0796 5.79041 21.5836 4.38405 21.0522 3.46374C20.5134 2.53051 19.0095 2.26939 16.9997 2.59929"
                            stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                    </svg>
                    <span class="top-bar-text">International website</span>
                </div>
            </div>
        </div>

        <!-- Main Navigation -->
        <div class="bg-transparent py-2 font-roboto lg:px-[150px] px-4">
            <nav class="floating-navbar bg-white my-4 px-6">
                <!-- Mobile Navigation -->
                <div class="lg:hidden flex items-center justify-between py-4">
                    <!-- Mobile Menu Button -->
                    <button class="flex items-center justify-center w-8 h-8" id="mobile-menu-btn">
                        <svg class="w-6 h-6 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>

                    <!-- Mobile Logo -->
                    <img class="h-12" src="assets/logo.png" alt="logo" />

                    <!-- Mobile BIKES Button -->
                    <button class="text-sm font-medium text-gray-700 flex items-center space-x-1" id="mobile-bikes-btn">
                        <span>BIKES</span>
                        <svg class="w-4 h-4 transition-transform duration-200" id="mobile-bikes-arrow" fill="none"
                            stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7">
                            </path>
                        </svg>
                    </button>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden lg:flex justify-evenly items-center py-4 text-base">
                    <!-- Left Navigation Items -->
                    <div class="flex items-center space-x-8">
                        <!-- Motorcycles Dropdown -->
                        <div class="relative dropdown">
                            <button
                                class="text-sm flex items-center space-x-1 text-gray-700 hover:text-blue-600 transition-colors duration-200"
                                id="motorcycles-dropdown-btn">
                                <span>MOTORCYCLES</span>
                                <svg class="w-4 h-4 transition-transform duration-200" id="motorcycles-arrow"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>

                            <!-- Dropdown Content -->
                            <div id="motorcycles-dropdown"
                                class="dropdown-content absolute top-full left-0 mt-12 bg-white border border-gray-200 z-50 overflow-hidden hidden"
                                style="width: 1000px; height: 600px">
                                <div class="flex h-full">
                                    <!-- Left Sidebar - Brands -->
                                    <div class="w-64 bg-gray-50 p-6 rounded-l-lg flex-shrink-0">
                                        <h3 class="text-gray-800 font-semibold mb-4">BRANDS</h3>
                                        <ul class="space-y-2" id="brand-list">
                                            <!-- Brand items will be generated dynamically by JavaScript -->
                                        </ul>
                                    </div>

                                    <!-- Right Content - Motorcycles -->
                                    <div class="flex-1 flex flex-col h-full">
                                        <!-- Category Filter -->
                                        <div class="flex space-x-4 p-6 pb-4 flex-shrink-0 border-b border-gray-100"
                                            id="category-buttons-container">
                                            <!-- Category buttons will be generated dynamically by JavaScript -->
                                        </div>

                                        <!-- Motorcycle Grid -->
                                        <div id="motorcycle-grid" class="flex-1 overflow-y-auto px-6 py-4">
                                            <!-- Motorcycle sections will be generated dynamically by JavaScript -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <a href="#"
                            class="text-gray-700 text-sm hover:text-blue-600 transition-colors duration-200">SHOWROOMS</a>
                        <a href="#"
                            class="text-gray-700 text-sm hover:text-blue-600 transition-colors duration-200">WORKSHOPS</a>
                        <a href="#"
                            class="text-gray-700 text-sm hover:text-blue-600 transition-colors duration-200">EVENTS</a>
                    </div>

                    <!-- Center Logo -->
                    <img class="h-[72px] px-4" src="assets/logo.png" alt="logo" />

                    <!-- Right Navigation Items -->
                    <div class="flex text-sm items-center space-x-8">
                        <a href="#" class="text-gray-700 hover:text-blue-600 transition-colors duration-200">BOOK TEST
                            RIDE</a>
                        <a href="/about.html"
                            class="text-gray-700 hover:text-blue-600 transition-colors duration-200">ABOUT US</a>
                        <a href="#" class="text-gray-700 hover:text-blue-600 transition-colors duration-200">NEWS</a>

                        <!-- Media Center Dropdown -->
                        <div class="relative dropdown">
                            <button
                                class="flex items-center space-x-1 text-gray-700 hover:text-blue-600 transition-colors duration-200"
                                id="media-dropdown-btn">
                                <span>MEDIA CENTER</span>
                                <svg class="w-4 h-4 transition-transform duration-200" id="media-arrow" fill="none"
                                    stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>

                            <!-- Media Dropdown Content -->
                            <div id="media-dropdown"
                                class="media-dropdown-content absolute top-full right-0 mt-12 bg-white border border-gray-200 z-50 hidden"
                                style="width: 220px">
                                <div class="py-2">
                                    <a href="/about.html"
                                        class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-blue-600 transition-colors duration-200">ABOUT
                                        US</a>
                                    <a href="#"
                                        class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-blue-600 transition-colors duration-200">ANNOUNCEMENTS</a>
                                    <a href="#"
                                        class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-blue-600 transition-colors duration-200">EVENTS</a>
                                    <a href="/blogs.html"
                                        class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-blue-600 transition-colors duration-200">BLOGS</a>
                                    <a href="#"
                                        class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-blue-600 transition-colors duration-200">DOWNLOAD
                                        CENTER</a>
                                    <a href="#"
                                        class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-blue-600 transition-colors duration-200">CONTACT
                                        US</a>
                                    <a href="/faqs.html"
                                        class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-blue-600 transition-colors duration-200">FAQS</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </nav>

            <!-- Mobile Bikes Full-Screen Menu -->
            <div id="mobile-bikes-dropdown"
                class="lg:hidden fixed inset-0 bg-white z-50 transform translate-x-full transition-transform duration-300">
                <!-- Mobile Bikes Content -->
                <div class="h-full flex flex-col">
                    <!-- Header -->
                    <div class="flex items-center justify-between p-4 border-b border-gray-200">
                        <button class="back-btn">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M15 19l-7-7 7-7"></path>
                            </svg>
                        </button>
                        <h2 class="text-lg font-semibold">BIKES</h2>
                        <button class="close-btn">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>

                    <!-- Brands List -->
                    <div id="mobile-bikes-brands" class="flex-1 overflow-y-auto">
                        <!-- Mobile brand items will be generated dynamically by JavaScript -->
                    </div>
                </div>
            </div>

            <!-- Mobile Menu Full-Screen -->
            <div id="mobile-menu"
                class="lg:hidden fixed inset-0 bg-white z-50 transform translate-x-full transition-transform duration-300">
                <!-- Mobile Menu Content -->
                <div class="h-full flex flex-col">
                    <!-- Header -->
                    <div class="flex items-center justify-between p-4 border-b border-gray-200">
                        <img class="h-8" src="assets/logo.png" alt="logo" />
                        <button class="close-btn">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>

                    <!-- Menu Items -->
                    <div class="flex-1 overflow-y-auto">
                        <div class="mobile-menu-item">
                            <span class="text-lg font-medium text-gray-900">MOTORCYCLES</span>
                            <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7">
                                </path>
                            </svg>
                        </div>
                        <a href="#" class="mobile-menu-item">
                            <span class="text-lg font-medium text-gray-900">SHOWROOMS</span>
                        </a>
                        <a href="#" class="mobile-menu-item">
                            <span class="text-lg font-medium text-gray-900">WORKSHOPS</span>
                        </a>
                        <a href="#" class="mobile-menu-item">
                            <span class="text-lg font-medium text-gray-900">EVENTS</span>
                        </a>
                        <a href="#" class="mobile-menu-item">
                            <span class="text-lg font-medium text-gray-900">BOOK TEST RIDE</span>
                        </a>
                        <a href="/about.html" class="mobile-menu-item">
                            <span class="text-lg font-medium text-gray-900">ABOUT US</span>
                        </a>
                        <a href="#" class="mobile-menu-item">
                            <span class="text-lg font-medium text-gray-900">NEWS</span>
                        </a>
                        <div class="mobile-menu-item">
                            <span class="text-lg font-medium text-gray-900">MEDIA CENTER</span>
                            <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7">
                                </path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Hero Carousel Section -->
    <section class="hero-carousel relative">
        <div class="carousel-container">
            <!-- Slide 1 -->
            <div class="carousel-slide active" id="hero-slide-1">
                <img src="./assets/hero_image_1.png" alt="Hero Image 1" class="w-full h-full object-cover" />
            </div>

            <!-- Slide 2 -->
            <div class="carousel-slide" id="hero-slide-2">
                <img src="https://images.unsplash.com/photo-1609630875171-b1321377ee65?w=1920&h=1080&fit=crop"
                    alt="Hero Image 2" class="w-full h-full object-cover" />
            </div>

            <!-- Slide 3 -->
            <div class="carousel-slide" id="hero-slide-3">
                <img src="https://images.unsplash.com/photo-1568772585407-9361f9bf3a87?w=1920&h=1080&fit=crop"
                    alt="Hero Image 3" class="w-full h-full object-cover" />
            </div>

            <!-- Slide 4 -->
            <div class="carousel-slide" id="hero-slide-4">
                <img src="https://images.unsplash.com/photo-1605531179818-de32686e5e2e?w=1920&h=1080&fit=crop"
                    alt="Hero Image 4" class="w-full h-full object-cover" />
            </div>

            <!-- Slide 5 -->
            <div class="carousel-slide" id="hero-slide-5">
                <img src="https://images.unsplash.com/photo-1571068316344-75bc76f77890?w=1920&h=1080&fit=crop"
                    alt="Hero Image 5" class="w-full h-full object-cover" />
            </div>

            <!-- Carousel Controls -->
            <button class="carousel-control prev" aria-label="Previous slide">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
            </button>
            <button class="carousel-control next" aria-label="Next slide">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
            </button>

            <!-- Carousel Indicators -->
            <div class="carousel-indicators">
                <button class="indicator active" data-slide="0" aria-label="Go to slide 1"></button>
                <button class="indicator" data-slide="1" aria-label="Go to slide 2"></button>
                <button class="indicator" data-slide="2" aria-label="Go to slide 3"></button>
                <button class="indicator" data-slide="3" aria-label="Go to slide 4"></button>
                <button class="indicator" data-slide="4" aria-label="Go to slide 5"></button>
            </div>
        </div>
    </section>

    <!-- Bike Carousel Section -->
    <section class="w-full bg-white relative overflow-hidden">
        <!-- Background Text -->
        <div class="absolute inset-0 flex items-center justify-center pointer-events-none">
            <h1 class="brand-text pulsar-text text-[15vw] lg:text-[20vw] font-black text-indigo-100 select-none"
                id="background-brand-text">
                PULSAR
            </h1>
        </div>

        <!-- Brand Navigation Tabs - Top -->
        <div class="relative z-10 pt-8 px-12">
            <nav class="flex justify-center">
                <div class="inline-flex space-x-8 lg:space-x-16 border-b border-gray-200 pl-8 pr-8"
                    id="brand-tabs-container">
                    <!-- Brand tabs will be generated dynamically by JavaScript -->

                    <!-- STATIC EXAMPLE (for reference only - not used):
          <button class="brand-tab active text-lg lg:text-xl font-semibold text-gray-800 border-b-2 border-black pb-2"
            data-brand="PULSAR" data-logo="./assets/brand-logos/pulsar-logo.png" data-category="Sports"
            data-category-icon="./assets/icons/sports.png">
            PULSAR
          </button>
          <button
            class="brand-tab text-lg lg:text-xl font-semibold text-gray-400 border-b-2 border-transparent pb-2 hover:text-gray-600"
            data-brand="DOMINAR" data-logo="./assets/brand-logos/dominar-logo.svg" data-category="Touring"
            data-category-icon="./assets/icons/touring.png">
            DOMINAR
          </button>
          <button
            class="brand-tab text-lg lg:text-xl font-semibold text-gray-400 border-b-2 border-transparent pb-2 hover:text-gray-600"
            data-brand="AVENGERS" data-logo="./assets/brand-logos/avengers-logo.svg" data-category="Adventure"
            data-category-icon="./assets/icons/adventure.png">
            AVENGERS
          </button>
          <button
            class="brand-tab text-lg lg:text-xl font-semibold text-gray-400 border-b-2 border-transparent pb-2 hover:text-gray-600"
            data-brand="DISCOVER" data-logo="./assets/brand-logos/discover-logo.svg" data-category="Commuter"
            data-category-icon="./assets/icons/commuter.png">
            DISCOVER
          </button>
          <button
            class="brand-tab text-lg lg:text-xl font-semibold text-gray-400 border-b-2 border-transparent pb-2 hover:text-gray-600"
            data-brand="PLATINA" data-logo="./assets/brand-logos/platina-logo.svg" data-category="Economy"
            data-category-icon="./assets/icons/economy.png">
            PLATINA
          </button>
          -->
                </div>
            </nav>
        </div>

        <!-- Main Content Area -->
        <div class="relative z-10 max-w-7xl mx-auto px-4 py-8">
            <!-- Bike Title and Description -->
            <div class="text-center mb-8">
                <h2 class="text-3xl lg:text-5xl font-bold text-gray-900 mb-4" id="bike-title">
                    PULSAR 220F ABS
                </h2>
                <p class="text-gray-600 text-base lg:text-lg max-w-3xl mx-auto leading-relaxed" id="bike-description">
                    Experience the perfect blend of power and style with the Pulsar 220F
                    ABS.
                </p>
            </div>

            <!-- Main Bike Display Grid -->
            <div class="grid grid-cols-12 gap-4 lg:gap-8 items-center min-h-[500px]">
                <!-- Left Side - Navigation Arrow -->
                <div class="col-span-2 flex justify-center items-center">
                    <button
                        class="bike-prev-btn w-12 h-12 lg:w-14 lg:h-14 rounded-full border border-gray-300 flex items-center justify-center hover:border-gray-500 hover:bg-gray-50 transition-all duration-200"
                        id="bike-prev-btn">
                        <svg class="w-6 h-6 lg:w-7 lg:h-7" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7">
                            </path>
                        </svg>
                    </button>
                </div>

                <!-- Center - Main Bike Image -->
                <div class="col-span-8 flex justify-center items-center">
                    <div class="relative w-full max-w-4xl">
                        <img src="./assets/bikes/pulsar/pulsar_220f_abs.png" alt="Pulsar 220F ABS"
                            class="w-full h-auto max-h-[400px] lg:max-h-[500px] object-contain" id="main-bike-image" />
                    </div>
                </div>

                <!-- Right Side - Navigation Arrow -->
                <div class="col-span-2 flex justify-center items-center">
                    <button
                        class="bike-next-btn w-12 h-12 lg:w-14 lg:h-14 rounded-full border border-gray-300 flex items-center justify-center hover:border-gray-500 hover:bg-gray-50 transition-all duration-200"
                        id="bike-next-btn">
                        <svg class="w-6 h-6 lg:w-7 lg:h-7" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7">
                            </path>
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Bottom Section -->
            <div class="mt-8">
                <!-- Brand Info and Color Selection Row -->
                <div class="flex items-center justify-between mb-6 px-4 gap-4 flex-wrap md:flex-nowrap">
                    <!-- Left: Brand Logo + Category -->
                    <div class="flex items-center space-x-4 min-w-0">
                        <img src="./assets/brand-logos/pulsar-logo.png" alt="Pulsar Logo" class="h-8" id="brand-logo" />
                        <div class="flex items-center space-x-2">
                            <img src="./assets/icons/sports.png" alt="Sports Category" class="h-6" id="category-icon" />
                            <span class="text-gray-600" id="category-text">NS</span>
                        </div>
                    </div>

                    <!-- Right: Color Selection -->
                    <div class="flex items-center space-x-4 shrink-0" id="color-selection">
                        <!-- Color buttons will be generated dynamically by JavaScript -->
                    </div>
                </div>


                <!-- Series Link -->
                <div class="w-full px-4 mb-6">
                    <a href="bike-detail.html"
                        class="inline-flex items-center justify-end hover:text-blue-800 transition-colors duration-200">
                        View Series page
                        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7">
                            </path>
                        </svg>
                    </a>
                </div>


                <!-- Model Tabs -->
                <div class="flex flex-wrap justify-end gap-4 pt-4" id="model-tabs-container">
                    <!-- Model tabs will be generated dynamically by JavaScript -->

                    <!-- STATIC EXAMPLE (for reference only - not used):
          <!-- Pulsar Models -->
                    <div class="model-group pulsar-models">
                        <div class="inline-flex flex-wrap gap-2 border-t border-gray-200 px-4">
                            <button
                                class="variant-btn active px-4 py-2 text-sm font-medium text-gray-700 border-t-2 border-black"
                                data-model="pulsar-220f-abs" data-image="./assets/bikes/pulsar/pulsar_220f_abs.png"
                                data-name="PULSAR 220F ABS"
                                data-description="Experience the perfect blend of power and style with the Pulsar 220F ABS. This high-performance motorcycle delivers an exhilarating ride with advanced features and superior handling.">
                                PULSAR 220F ABS
                            </button>
                            <button
                                class="variant-btn px-4 py-2 text-sm font-medium text-gray-500 border-t-2 border-transparent"
                                data-model="pulsar-150-td" data-image="./assets/bikes/pulsar/pulsar_150_td.png"
                                data-name="PULSAR 150 TD"
                                data-description="The Pulsar 150 TD combines style with efficiency, offering a perfect balance of performance and fuel economy for everyday riding.">
                                PULSAR 150 TD
                            </button>
                            <button
                                class="variant-btn px-4 py-2 text-sm font-medium text-gray-500 border-t-2 border-transparent"
                                data-model="pulsar-ns200" data-image="./assets/bikes/pulsar/pulsar_ns200.png"
                                data-name="PULSAR NS200"
                                data-description="The Pulsar NS200 is a sporty naked street bike that offers an exciting riding experience with its powerful engine and agile handling.">
                                PULSAR NS200
                            </button>
                            <button
                                class="variant-btn px-4 py-2 text-sm font-medium text-gray-500 border-t-2 border-transparent"
                                data-model="pulsar-rs200" data-image="./assets/bikes/pulsar/pulsar_rs200.png"
                                data-name="PULSAR RS200"
                                data-description="The Pulsar RS200 is a fully-faired sports bike that combines aggressive styling with high performance for an exhilarating ride.">
                                PULSAR RS200
                            </button>
                        </div>
                    </div>

                    <!-- Dominar Models -->
                    <div class="model-group dominar-models hidden">
                        <div class="inline-flex flex-wrap gap-2 border-t border-gray-200 px-4">
                            <button
                                class="variant-btn px-4 py-2 text-sm font-medium text-gray-500 border-t-2 border-transparent"
                                data-model="dominar-400" data-image="./assets/bikes/dominar/dominar_400.png"
                                data-name="DOMINAR 400"
                                data-description="The Dominar 400 is a powerful touring motorcycle designed for long-distance comfort and performance.">
                                DOMINAR 400
                            </button>
                            <button
                                class="variant-btn px-4 py-2 text-sm font-medium text-gray-500 border-t-2 border-transparent"
                                data-model="dominar-250" data-image="./assets/bikes/dominar/dominar_250.png"
                                data-name="DOMINAR 250"
                                data-description="The Dominar 250 offers a perfect balance of power and efficiency for urban commuting and weekend getaways.">
                                DOMINAR 250
                            </button>
                        </div>
                    </div>

                    <!-- Avengers Models -->
                    <div class="model-group avengers-models hidden">
                        <div class="inline-flex flex-wrap gap-2 border-t border-gray-200 px-4">
                            <button
                                class="variant-btn px-4 py-2 text-sm font-medium text-gray-500 border-t-2 border-transparent"
                                data-model="avenger-220" data-image="./assets/bikes/avengers/avenger_220.png"
                                data-name="AVENGER 220"
                                data-description="The Avenger 220 combines cruiser comfort with sporty performance for an unmatched riding experience.">
                                AVENGER 220
                            </button>
                            <button
                                class="variant-btn px-4 py-2 text-sm font-medium text-gray-500 border-t-2 border-transparent"
                                data-model="avenger-160" data-image="./assets/bikes/avengers/avenger_160.png"
                                data-name="AVENGER 160"
                                data-description="The Avenger 160 offers the perfect blend of style and comfort for urban cruising.">
                                AVENGER 160
                            </button>
                        </div>
                    </div>

                    <!-- Discover Models -->
                    <div class="model-group discover-models hidden">
                        <div class="inline-flex flex-wrap gap-2 border-t border-gray-200 px-4">
                            <button
                                class="variant-btn px-4 py-2 text-sm font-medium text-gray-500 border-t-2 border-transparent"
                                data-model="discover-125" data-image="./assets/bikes/discover/discover_125.png"
                                data-name="DISCOVER 125"
                                data-description="The Discover 125 is designed for efficient urban commuting with its fuel-efficient engine and comfortable riding position.">
                                DISCOVER 125
                            </button>
                            <button
                                class="variant-btn px-4 py-2 text-sm font-medium text-gray-500 border-t-2 border-transparent"
                                data-model="discover-110" data-image="./assets/bikes/discover/discover_110.png"
                                data-name="DISCOVER 110"
                                data-description="The Discover 110 offers excellent fuel efficiency and low maintenance costs for daily commuting.">
                                DISCOVER 110
                            </button>
                        </div>
                    </div>

                    <!-- Platina Models -->
                    <div class="model-group platina-models hidden">
                        <div class="inline-flex flex-wrap gap-2 border-t border-gray-200 px-4">
                            <button
                                class="variant-btn px-4 py-2 text-sm font-medium text-gray-500 border-t-2 border-transparent"
                                data-model="platina-110" data-image="./assets/bikes/platina/platina_110.png"
                                data-name="PLATINA 110"
                                data-description="The Platina 110 is Bajaj's most fuel-efficient motorcycle, perfect for budget-conscious riders.">
                                PLATINA 110
                            </button>
                            <button
                                class="variant-btn px-4 py-2 text-sm font-medium text-gray-500 border-t-2 border-transparent"
                                data-model="platina-100" data-image="./assets/bikes/platina/platina_100.png"
                                data-name="PLATINA 100"
                                data-description="The Platina 100 offers unbeatable fuel efficiency and low maintenance costs for daily commuting.">
                                PLATINA 100
                            </button>
                        </div>
                    </div>
                    -->
                </div>
            </div>
        </div>
    </section>

    <!-- Own Your Dream Bajaj Section -->
    <section class="py-16 dream-bajaj-section">
        <div class="max-w-7xl mx-auto px-6">
            <div class="grid lg:grid-cols-2 gap-12 items-center">
                <!-- Image Section -->
                <div class="relative">
                    <div class="rounded-2xl overflow-hidden shadow-2xl dream-bajaj-image">
                        <img src="/assets/dream-section-img.png" alt="Bajaj Motorcycle with Customer"
                            class="w-full h-[400px] object-cover" />
                    </div>
                </div>

                <!-- Content Section -->
                <div class="space-y-6">
                    <h2 class="text-4xl font-bold text-gray-900 leading-tight text-failure">
                        OWN YOUR DREAM BAJAJ <br />
                        MOTORCYCLE WITH EASE
                    </h2>

                    <p class="text-lg text-gray-600 leading-relaxed">
                        At Bajaj Nepal, we believe that your dream ride should be within
                        reach. Our simplified financing options are designed to make
                        motorcycle ownership accessible and hassle-free for everyone.
                    </p>

                    <div class="pt-4">
                        <button
                            class="loan-availability-btn bg-[#222222] h-10 flex items-center text-white px-8 py-4 rounded-full font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                            Check Your Loan Availability
                            <svg class="w-6 h-6 group-hover:scale-110 transition-transform" fill="none"
                                stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7">
                                </path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Blog Section -->
    <section class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Blog Grid - Static Content -->
            <div class="grid grid-cols-1 lg:grid-cols-10 gap-6">
                <!-- Featured Blog Text -->
                <div class="group cursor-pointer featured-text-column">
                    <article class="overflow-hidden transition-all duration-300 h-full">
                        <div class="">
                            <div class="mb-4">
                                <span class="text-sm text-gray-500 font-medium">May 12, 2025</span>
                            </div>
                            <h3
                                class="text-2xl lg:text-3xl font-bold text-gray-900 mb-4 group-hover:text-blue-600 transition-colors">
                                WHEELS OF MEMORY
                            </h3>
                            <p class="text-gray-600 mb-6 leading-relaxed">
                                Join us for the 12 months photography contest inviting riders to share bike photos
                                symbolizing each of the 12 Indian months. Capture the essence of time and memories on
                                two wheels.
                            </p>
                            <button
                                class="inline-flex items-center bg-gray-900 text-white px-6 py-2 rounded-full font-medium hover:bg-gray-800 transition-colors group">
                                <span>LEARN MORE</span>
                                <svg class="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" fill="none"
                                    stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7"></path>
                                </svg>
                            </button>
                        </div>
                    </article>
                </div>

                <!-- Featured Blog Image -->
                <div class="group cursor-pointer featured-image-column">
                    <div
                        class="relative h-80 lg:h-96 overflow-hidden rounded-xl shadow-md hover:shadow-lg transition-all duration-300">
                        <img src="https://images.unsplash.com/photo-1508357941501-0924cf312bbd?q=80&w=2070&auto=format&fit=crop"
                            alt="Wheels of Memory Photography Contest"
                            class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500"
                            loading="lazy" />
                        <div class="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent">
                        </div>
                    </div>
                </div>

                <!-- Bottom Row Container -->
                <div class="lg:col-span-10" style="grid-column: 1 / -1">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
                        <!-- Blog Card 1 -->
                        <article class="cursor-pointer  overflow-hidden">
                            <div class="relative h-52 overflow-hidden">
                                <img src="https://images.unsplash.com/photo-1568772585407-9361f9bf3a87?w=600&h=400&fit=crop&crop=center"
                                    alt="Best Motorcycle Routes in Nepal"
                                    class="w-full h-full object-cover rounded-lg hover:scale-105 transition-transform duration-500"
                                    loading="lazy" />
                            </div>
                            <div class="mt-4">
                                <h4 class="text-gray-600 text-base">
                                    Best Motorcycle Routes in Nepal
                                </h4>

                            </div>
                        </article>

                        <!-- Blog Card 2 -->
                        <a href="s" class="cursor-pointer  overflow-hidden">
                            <div class="relative h-52 overflow-hidden">
                                <img src="https://images.unsplash.com/photo-1568772585407-9361f9bf3a87?w=600&h=400&fit=crop&crop=center"
                                    alt="Best Motorcycle Routes in Nepal"
                                    class="w-full h-full object-cover rounded-lg hover:scale-105 transition-transform duration-500"
                                    loading="lazy" />
                            </div>
                            <div class="mt-4">
                                <h4 class="text-gray-600 text-base">
                                    Best Motorcycle Routes in Nepal
                                </h4>

                            </div>
                        </a>

                        <!-- Blog Card 3 -->
                        <article class="cursor-pointer  overflow-hidden">
                            <div class="relative h-52 overflow-hidden">
                                <img src="https://images.unsplash.com/photo-1568772585407-9361f9bf3a87?w=600&h=400&fit=crop&crop=center"
                                    alt="Best Motorcycle Routes in Nepal"
                                    class="w-full h-full object-cover rounded-lg hover:scale-105 transition-transform duration-500"
                                    loading="lazy" />
                            </div>
                            <div class="mt-4">
                                <h4 class="text-gray-600 text-base">
                                    Best Motorcycle Routes in Nepal
                                </h4>

                            </div>
                        </article>
                    </div>
                </div>
            </div>

            <!-- View All Blogs Button -->
            <div class="text-center mt-12">
                <button
                    class="bg-gray-900 text-white px-8 py-3 rounded-full font-semibold hover:bg-gray-800 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                    View All Stories
                </button>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-50 min-h-screen flex flex-col">
        <!-- Email Signup Section -->
        <div class="flex-1 flex items-center justify-center px-4 py-12">
            <div class="max-w-md w-full">
                <div class="text-center mb-8">
                    <h2 class="text-2xl font-bold text-gray-900 mb-2">
                        Sign up for Email
                    </h2>
                    <p class="text-sm text-gray-500 mb-1">
                        Read our
                        <a href="#" class="text-blue-500 underline">privacy policy</a>
                        to learn about data processing
                    </p>
                    <p class="text-sm text-gray-500">
                        Sign up for BAJAJ latest news and updates
                    </p>
                </div>

                <form id="emailForm" class="mb-4">
                    <div class="flex gap-2 mb-2">
                        <input type="email" id="email" placeholder="YOUR EMAIL ADDRESS"
                            class="flex-1 bg-white border border-gray-300 rounded-md px-4 py-3 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                            required />
                        <button type="submit"
                            class="bg-blue-500 text-white px-6 py-3 rounded-md text-sm font-medium hover:bg-blue-600">
                            SUBSCRIBE NOW
                        </button>
                    </div>
                    <p class="text-xs text-gray-500 text-center">
                        This site is protected by reCAPTCHA and the Google
                        <a href="#" class="underline">Privacy Policy</a> and
                        <a href="#" class="underline">Terms of Service</a> apply.
                    </p>
                </form>
            </div>
        </div>

        <!-- Footer Section -->
        <div class="bg-gray-900 text-white py-12">
            <div class="max-w-6xl mx-auto px-4">
                <div class="text-center mb-8">
                    <div class="flex justify-center items-center mb-4">
                        <div class="w-8 h-8 mr-3 bg-white rounded-full flex items-center justify-center">
                            <span class="text-black font-bold text-sm">G</span>
                        </div>
                        <h3 class="text-lg font-medium">
                            GOLCHHA GROUP WITH LEGACY OF 100 YEAR
                        </h3>
                    </div>
                </div>

                <!-- Footer Links -->
                <div class="flex justify-center gap-8 text-sm mb-8">
                    <a href="#" class="hover:text-gray-300">TERMS OF USE</a>
                    <a href="#" class="hover:text-gray-300">PRIVACY INFORMATION</a>
                    <a href="#" class="hover:text-gray-300">COOKIES INFORMATION</a>
                </div>

                <!-- Copyright -->
                <div class="text-center text-xs text-gray-400 mb-8">
                    <p>
                        Copyright © 2025 Bajaj Auto Ltd – A Sole Shareholder Company - A
                        Company subject to the Management and Coordination
                    </p>
                    <p>activities of BAJAJ AUTO. All rights reserved. VAT NO.</p>
                </div>

                <!-- Bottom Section -->
                <div class="flex flex-wrap justify-between items-center gap-4">
                    <!-- Bajaj Logo -->
                    <div>
                        <img src="../assets/footer-logo.svg" alt="" />
                    </div>

                    <!-- Social Media Icons -->
                    <div class="flex gap-4">
                        <a href="#"
                            class="text-gray-400 hover:text-white transition-transform transform hover:-translate-y-1">
                            <i class="fab fa-instagram"></i>
                        </a>
                        <a href="#"
                            class="text-gray-400 hover:text-white transition-transform transform hover:-translate-y-1">
                            <i class="fab fa-facebook"></i>
                        </a>
                        <a href="#"
                            class="text-gray-400 hover:text-white transition-transform transform hover:-translate-y-1">
                            <i class="fab fa-youtube"></i>
                        </a>
                        <a href="#"
                            class="text-gray-400 hover:text-white transition-transform transform hover:-translate-y-1">
                            <i class="fab fa-tiktok"></i>
                        </a>
                        <a href="#"
                            class="text-gray-400 hover:text-white transition-transform transform hover:-translate-y-1">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#"
                            class="text-gray-400 hover:text-white transition-transform transform hover:-translate-y-1">
                            <i class="fab fa-linkedin"></i>
                        </a>
                    </div>

                    <!-- International Website -->
                    <div class="flex items-center text-sm text-gray-400">
                        <i class="fas fa-globe mr-2"></i>
                        <a href="#" class="hover:text-white">International website</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script>
        document
            .getElementById("emailForm")
            .addEventListener("submit", function (e) {
                e.preventDefault();
                const email = document.getElementById("email").value;
                if (email) {
                    alert(
                        "Thank you for subscribing! You will receive updates at: " + email
                    );
                    document.getElementById("email").value = "";
                }
            });
    </script>


    <!-- Hero Carousel Functionality Script -->
    <script>

        document.addEventListener("DOMContentLoaded", function () {
            console.log("Backup carousel script running...");

            // Check if elements exist
            const slides = document.querySelectorAll(".carousel-slide");
            const indicators = document.querySelectorAll(
                ".carousel-indicators .indicator"
            );
            const prevBtn = document.querySelector(".carousel-control.prev");
            const nextBtn = document.querySelector(".carousel-control.next");

            console.log("Backup - Slides:", slides.length);
            console.log("Backup - Indicators:", indicators.length);
            console.log("Backup - Prev button:", prevBtn);
            console.log("Backup - Next button:", nextBtn);

            if (slides.length > 0 && prevBtn && nextBtn) {
                let currentSlide = 0;
                let autoplayInterval;

                function showSlide(index) {
                    slides.forEach((slide) => slide.classList.remove("active"));
                    indicators.forEach((indicator) =>
                        indicator.classList.remove("active")
                    );

                    if (slides[index]) {
                        slides[index].classList.add("active");
                    }
                    if (indicators[index]) {
                        indicators[index].classList.add("active");
                    }
                    currentSlide = index;
                }

                function nextSlide() {
                    const next = (currentSlide + 1) % slides.length;
                    showSlide(next);
                }

                function prevSlide() {
                    const prev = (currentSlide - 1 + slides.length) % slides.length;
                    showSlide(prev);
                }

                function startAutoplay() {
                    stopAutoplay();
                    autoplayInterval = setInterval(nextSlide, 4000);
                }

                function stopAutoplay() {
                    if (autoplayInterval) {
                        clearInterval(autoplayInterval);
                    }
                }

                // Add event listeners
                prevBtn.addEventListener("click", function (e) {
                    e.preventDefault();
                    stopAutoplay();
                    prevSlide();
                    startAutoplay();
                });

                nextBtn.addEventListener("click", function (e) {
                    e.preventDefault();
                    stopAutoplay();
                    nextSlide();
                    startAutoplay();
                });

                indicators.forEach((indicator, index) => {
                    indicator.addEventListener("click", function (e) {
                        e.preventDefault();
                        stopAutoplay();
                        showSlide(index);
                        startAutoplay();
                    });
                });

                // Hover pause
                const carousel = document.querySelector(".hero-carousel");
                if (carousel) {
                    carousel.addEventListener("mouseenter", stopAutoplay);
                    carousel.addEventListener("mouseleave", startAutoplay);
                }

                // Start autoplay
                startAutoplay();
                console.log("Backup carousel initialized successfully");
            }
        });
    </script>

    <!-- Navbar Functionality Scripts -->
    <script>
        document.addEventListener("DOMContentLoaded", () => {
            // Brand-Category mapping
            const brandCategories = {
                PULSAR: ["All", "Classic", "NS", "N"],
                DOMINAR: ["All", "Adventure"],
                AVENGERS: ["All", "Cruiser"],
                DISCOVER: ["All", "Commuter"],
                PLATINA: ["All", "Economy"],
            };

            // Desktop Dropdown Functionality
            function toggleDropdown(dropdownId) {
                const dropdown = document.getElementById(dropdownId + "-dropdown");
                const arrow = document.getElementById(dropdownId + "-arrow");

                if (!dropdown || !arrow) return;

                // Close all other dropdowns
                document
                    .querySelectorAll(".dropdown-content, .media-dropdown-content")
                    .forEach((dd) => {
                        if (dd !== dropdown) {
                            dd.classList.remove("show");
                            dd.classList.add("hidden");
                            // Reset arrow rotation for other dropdowns
                            const otherArrow = dd.parentElement.querySelector("svg");
                            if (otherArrow) {
                                otherArrow.style.transform = "rotate(0deg)";
                            }
                        }
                    });

                // Toggle current dropdown
                if (dropdown.classList.contains("hidden")) {
                    dropdown.classList.remove("hidden");
                    dropdown.classList.add("show");
                    arrow.style.transform = "rotate(180deg)";
                } else {
                    dropdown.classList.remove("show");
                    dropdown.classList.add("hidden");
                    arrow.style.transform = "rotate(0deg)";
                }
            }

            // Brand Filtering with Category Update
            function filterByBrand(brand) {
                // Update active brand
                document.querySelectorAll(".brand-item").forEach((item) => {
                    item.classList.toggle("active", item.dataset.brand === brand);
                });

                // Show/hide motorcycle sections
                document
                    .querySelectorAll(".motorcycle-section")
                    .forEach((section) => {
                        section.classList.toggle(
                            "active",
                            section.dataset.brand === brand
                        );
                    });

                // Update category buttons based on selected brand
                updateCategoryButtons(brand);

                // Reset category filter to 'All'
                filterByCategory("All");
            }

            // Update category buttons based on selected brand
            function updateCategoryButtons(brand) {
                const availableCategories = brandCategories[brand] || ["All"];

                document.querySelectorAll(".category-btn").forEach((btn) => {
                    const category = btn.dataset.category;
                    if (availableCategories.includes(category)) {
                        btn.style.display = "block";
                        btn.disabled = false;
                    } else {
                        btn.style.display = "none";
                        btn.disabled = true;
                    }
                });
            }

            // Category Filtering
            function filterByCategory(category) {
                // Update active category button
                document.querySelectorAll(".category-btn").forEach((btn) => {
                    btn.classList.toggle("active", btn.dataset.category === category);
                });

                // Show/hide category sections
                document.querySelectorAll(".category-section").forEach((section) => {
                    if (category === "All") {
                        section.style.display = "block";
                    } else {
                        section.style.display =
                            section.dataset.category === category ? "block" : "none";
                    }
                });
            }

            // Mobile Menu Functionality
            const mobileMenu = {
                menu: document.getElementById("mobile-menu"),
                bikesDropdown: document.getElementById("mobile-bikes-dropdown"),
                menuBtn: document.getElementById("mobile-menu-btn"),
                bikesBtn: document.getElementById("mobile-bikes-btn"),
                closeBtns: document.querySelectorAll(".close-btn"),
                backBtns: document.querySelectorAll(".back-btn"),

                init() {
                    // Mobile menu toggle
                    this.menuBtn?.addEventListener("click", () => this.toggleMenu());
                    this.bikesBtn?.addEventListener("click", () =>
                        this.toggleBikesDropdown()
                    );

                    // Close buttons
                    this.closeBtns.forEach((btn) => {
                        btn.addEventListener("click", () => {
                            this.menu.classList.add("translate-x-full");
                            this.bikesDropdown.classList.add("translate-x-full");
                        });
                    });

                    // Back buttons
                    this.backBtns.forEach((btn) => {
                        btn.addEventListener("click", () => {
                            this.bikesDropdown.classList.add("translate-x-full");
                        });
                    });

                    // Brand items in mobile view
                    document.querySelectorAll(".mobile-brand-item").forEach((item) => {
                        item.addEventListener("click", () => {
                            const brand = item.querySelector("span").textContent;
                            filterByBrand(brand);
                            this.bikesDropdown.classList.add("translate-x-full");
                        });
                    });
                },

                toggleMenu() {
                    this.menu.classList.toggle("translate-x-full");
                    if (!this.menu.classList.contains("translate-x-full")) {
                        this.bikesDropdown.classList.add("translate-x-full");
                    }
                },

                toggleBikesDropdown() {
                    this.bikesDropdown.classList.toggle("translate-x-full");
                    if (!this.bikesDropdown.classList.contains("translate-x-full")) {
                        this.menu.classList.add("translate-x-full");
                    }
                },
            };

            // Close dropdowns when clicking outside
            function closeDropdownsOnOutsideClick(event) {
                const isDropdownClick = event.target.closest(".dropdown");
                const isDropdownContent = event.target.closest(
                    ".dropdown-content, .media-dropdown-content"
                );

                if (!isDropdownClick && !isDropdownContent) {
                    // Close all dropdowns
                    document
                        .querySelectorAll(".dropdown-content, .media-dropdown-content")
                        .forEach((dd) => {
                            dd.classList.remove("show");
                            dd.classList.add("hidden");
                        });

                    // Reset all arrows
                    document.querySelectorAll(".dropdown svg").forEach((arrow) => {
                        arrow.style.transform = "rotate(0deg)";
                    });
                }
            }

            // Initialize Event Listeners
            function initEventListeners() {
                // Desktop dropdowns - Fix for motorcycles and media center
                document
                    .getElementById("motorcycles-dropdown-btn")
                    ?.addEventListener("click", (e) => {
                        e.preventDefault();
                        toggleDropdown("motorcycles");
                    });

                document
                    .getElementById("media-dropdown-btn")
                    ?.addEventListener("click", (e) => {
                        e.preventDefault();
                        toggleDropdown("media");
                    });

                // Click outside to close dropdowns
                document.addEventListener("click", closeDropdownsOnOutsideClick);

                // Initialize mobile menu
                mobileMenu.init();

                // Initialize with PULSAR as default active brand
                filterByBrand("PULSAR");
            }

            // Function to generate navbar brand list dynamically
            function generateNavbarBrandList() {
                const brandList = document.getElementById("brand-list");
                if (!brandList) return;

                brandList.innerHTML = "";
                const brands = Object.keys(bikeData);

                brands.forEach((brand, index) => {
                    const li = document.createElement("li");
                    const a = document.createElement("a");
                    a.href = "#";
                    a.className = `brand-item ${index === 0 ? "active" : ""} block px-3 py-2 text-sm text-gray-700 rounded-md transition-colors duration-200`;
                    a.dataset.brand = brand;
                    a.textContent = brand;

                    a.addEventListener("click", (e) => {
                        e.preventDefault();
                        filterByBrand(brand);
                    });

                    li.appendChild(a);
                    brandList.appendChild(li);
                });
            }

            // Function to generate category buttons dynamically
            function generateCategoryButtons() {
                const categoryContainer = document.getElementById("category-buttons-container");
                if (!categoryContainer) return;

                categoryContainer.innerHTML = "";

                // Add "All" button first
                const allBtn = document.createElement("button");
                allBtn.className = "category-btn active px-4 py-2 rounded-full text-sm font-medium transition-all duration-200";
                allBtn.dataset.category = "All";
                allBtn.textContent = "All";
                allBtn.addEventListener("click", () => filterByCategory("All"));
                categoryContainer.appendChild(allBtn);

                // Get unique categories from all bikes
                const allCategories = new Set();
                Object.values(bikeData).forEach(brandData => {
                    brandData.bikes.forEach(bike => {
                        if (bike.category) {
                            allCategories.add(bike.category);
                        }
                    });
                });

                // Add category buttons
                allCategories.forEach(category => {
                    const btn = document.createElement("button");
                    btn.className = "category-btn px-4 py-2 rounded-full text-sm font-medium transition-all duration-200";
                    btn.dataset.category = category;
                    btn.textContent = category;
                    btn.addEventListener("click", () => filterByCategory(category));
                    categoryContainer.appendChild(btn);
                });
            }

            // Function to generate motorcycle grid dynamically
            function generateMotorcycleGrid() {
                const motorcycleGrid = document.getElementById("motorcycle-grid");
                if (!motorcycleGrid) return;

                motorcycleGrid.innerHTML = "";
                const brands = Object.keys(bikeData);

                brands.forEach((brand, brandIndex) => {
                    const brandData = bikeData[brand];
                    const brandSection = document.createElement("div");
                    brandSection.className = `motorcycle-section ${brandIndex === 0 ? "active" : ""}`;
                    brandSection.dataset.brand = brand;

                    // Group bikes by category
                    const bikesByCategory = {};
                    brandData.bikes.forEach(bike => {
                        if (!bikesByCategory[bike.category]) {
                            bikesByCategory[bike.category] = [];
                        }
                        bikesByCategory[bike.category].push(bike);
                    });

                    // Create category sections
                    Object.entries(bikesByCategory).forEach(([category, bikes]) => {
                        const categorySection = document.createElement("div");
                        categorySection.className = "category-section";
                        categorySection.dataset.category = category;

                        const grid = document.createElement("div");
                        grid.className = "grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6";

                        bikes.forEach(bike => {
                            const bikeCard = document.createElement("div");
                            bikeCard.className = "motorcycle-card";

                            const img = document.createElement("img");
                            img.src = bike.image;
                            img.alt = bike.name;
                            img.className = "w-full h-32 object-contain";

                            const h4 = document.createElement("h4");
                            h4.className = "text-sm font-medium mt-2";
                            h4.textContent = bike.name;

                            bikeCard.appendChild(img);
                            bikeCard.appendChild(h4);
                            grid.appendChild(bikeCard);
                        });

                        categorySection.appendChild(grid);
                        brandSection.appendChild(categorySection);
                    });

                    motorcycleGrid.appendChild(brandSection);
                });
            }

            // Function to generate mobile brand items dynamically
            function generateMobileBrandItems() {
                const mobileBrandsContainer = document.getElementById("mobile-bikes-brands");
                if (!mobileBrandsContainer) return;

                mobileBrandsContainer.innerHTML = "";
                const brands = Object.keys(bikeData);

                brands.forEach(brand => {
                    const brandItem = document.createElement("div");
                    brandItem.className = "mobile-brand-item";

                    const span = document.createElement("span");
                    span.className = "text-lg font-medium text-gray-900";
                    span.textContent = brand;

                    const svg = document.createElement("svg");
                    svg.className = "w-5 h-5 text-gray-400";
                    svg.setAttribute("fill", "none");
                    svg.setAttribute("stroke", "currentColor");
                    svg.setAttribute("viewBox", "0 0 24 24");
                    svg.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>';

                    brandItem.appendChild(span);
                    brandItem.appendChild(svg);

                    brandItem.addEventListener("click", () => {
                        filterByBrand(brand);
                        mobileMenu.bikesDropdown.classList.add("translate-x-full");
                    });

                    mobileBrandsContainer.appendChild(brandItem);
                });
            }

            // Make functions globally available for HTML onclick handlers
            window.toggleDropdown = toggleDropdown;
            window.filterByBrand = filterByBrand;
            window.filterByCategory = filterByCategory;

            // Initialize everything
            initEventListeners();

            // Generate navbar elements dynamically (if bikeData is available)
            if (typeof window.bikeData !== 'undefined') {
                generateNavbarBrandList();
                generateCategoryButtons();
                generateMotorcycleGrid();
                generateMobileBrandItems();
            } else {
                // If bikeData is not available yet, wait for it
                const checkBikeData = setInterval(() => {
                    if (typeof window.bikeData !== 'undefined') {
                        clearInterval(checkBikeData);
                        generateNavbarBrandList();
                        generateCategoryButtons();
                        generateMotorcycleGrid();
                        generateMobileBrandItems();
                        console.log("✅ Navbar elements generated from bikeData");
                    }
                }, 100);

                // Timeout after 5 seconds to prevent infinite checking
                setTimeout(() => {
                    clearInterval(checkBikeData);
                    console.warn("⚠️ bikeData not available after 5 seconds, navbar elements not generated");
                }, 5000);
            }
        });
    </script>

    <!-- Bike Carousel Functionality -->
    <script>
        document.addEventListener("DOMContentLoaded", function () {
            console.log("Initializing Bike Carousel...");

            // ========================================
            // DATA SECTION - REPLACE WITH LARAVEL DATA
            // ========================================

            /*
            LARAVEL DEVELOPER INSTRUCTIONS:
      
            1. Replace this entire bikeData object with Laravel Blade syntax like:
      
            const bikeData = @json($bikeData);
      
            2. In your Laravel controller, structure the data exactly like this:
      
            $bikeData = [
                'PULSAR' => [
                    'logo' => asset('assets/brand-logos/pulsar-logo.png'),
                    'category' => 'Sports',
                    'categoryIcon' => asset('assets/icons/sports.png'),
                    'bikes' => [
                        [
                            'id' => 'pulsar-220f-abs',
                            'name' => 'PULSAR 220F ABS',
                            'image' => asset('assets/bikes/pulsar/pulsar_220f_abs.png'),
                            'description' => 'Experience the perfect blend...',
                            'colors' => [
                                ['name' => 'black', 'color' => '#000000'],
                                ['name' => 'yellow', 'color' => '#facc15'],
                                // ... more colors
                            ]
                        ],
                        // ... more bikes
                    ]
                ],
                // ... more brands
            ];
      
            3. Pass this data to your Blade view:
            return view('your-view', compact('bikeData'));
            */

            // STATIC DATA STRUCTURE EXAMPLE (for reference):
            /*
            const staticBikeData = {
              PULSAR: {
                logo: "./assets/brand-logos/pulsar-logo.png",
                bikes: [
                  {
                    id: "pulsar-220f-abs",
                    name: "PULSAR 220F ABS",
                    image: "./assets/bikes/pulsar/pulsar_220f_abs.png",
                    category: "Sports",
                    categoryIcon: "./assets/icons/sports.png",
                    description: "Experience the perfect blend of power and style...",
                    colors: [
                      { name: "black", color: "#000000", image: "./assets/bikes/pulsar/pulsar_220f_abs_black.png", displayName: "Phantom Black" },
                      { name: "yellow", color: "#facc15", image: "./assets/bikes/pulsar/pulsar_220f_abs_yellow.png", displayName: "Lightning Yellow" },
                      { name: "green", color: "#16a34a", image: "./assets/bikes/pulsar/pulsar_220f_abs_green.png", displayName: "Racing Green" }
                    ]
                  }
                ]
              },
              DOMINAR: {
                logo: "./assets/brand-logos/dominar-logo.svg",
                bikes: [
                  {
                    id: "dominar-400",
                    name: "DOMINAR 400",
                    image: "./assets/bikes/dominar/dominar_400.png",
                    category: "Adventure",
                    categoryIcon: "./assets/icons/adventure.png",
                    description: "The Dominar 400 is a powerful touring motorcycle...",
                    colors: [
                      { name: "black", color: "#000000", image: "./assets/bikes/dominar/dominar_400_black.png", displayName: "Obsidian Black" },
                      { name: "blue", color: "#2563eb", image: "./assets/bikes/dominar/dominar_400_blue.png", displayName: "Royal Blue" }
                    ]
                  }
                ]
              }
            };
            */

            const bikeData = {
                PULSAR: {
                    logo: "./assets/brand-logos/pulsar-logo.png",
                    bikes: [
                        {
                            id: "pulsar-ns200",
                            name: "PULSAR NS200",
                            image: "https://cdn.bajajauto.com/-/media/assets/bajajauto/bikes/pulsarns200/ns200v-teaser-images/new-croppedteaser-webp/ns-webp/banner_w_1.webp",
                            category: "NS",
                            categoryIcon: "./assets/icons/ns.png",
                            description:
                                "The Pulsar NS200 is a sporty naked street bike that offers an exciting riding experience with its powerful engine and agile handling.",
                            colors: [
                                {
                                    name: "red",
                                    color: "#90020B",
                                    image: "https://cdn.bajajauto.com/-/media/assets/bajajauto/bikes/pulsarns200/newns200-360-degree/red/ns200-360-webp/00.png",
                                    displayName: "Cocktail Wine Red White"
                                },
                                {
                                    name: "black  ",
                                    color: "#111116",
                                    image: "https://cdn.bajajauto.com/-/media/assets/bajajauto/360degreeimages/bikes/pulsar/pulsar-ns-200/new-webp/ns-200-black-webp/00.png",
                                    displayName: "Glossy Ebony Black"
                                },
                                {
                                    name: "white",
                                    color: "#ffffff",
                                    image: "https://cdn.bajajauto.com/-/media/assets/bajajauto/bikes/pulsarns200/newns200-360-degree/ns-200-white-webp/ns-200-white-webp/00.png",
                                    displayName: "Metallic Pearl White"
                                },
                                {
                                    name: "blue",
                                    color: "#233d88",
                                    image: "https://cdn.bajajauto.com/-/media/assets/bajajauto/360degreeimages/bikes/pulsar/pulsar-ns-200/new-webp/ns-200-grey-webp/00.png",
                                    displayName: "Pewter Blue - Grey"
                                },
                            ],
                        },
                        {
                            id: "pulsar-220f-abs",
                            name: "PULSAR 220F ABS",
                            image: "https://cdn.bajajauto.com/-/media/assets/bajajauto/bikes/pulsar-k-2024/gallery/image6.webp",
                            category: "Classic",
                            categoryIcon: "./assets/category-icons/pulsar-category-icon.png",
                            description:
                                "Experience the perfect blend of power and style with the Pulsar 220F ABS. This high-performance motorcycle delivers an exhilarating ride with advanced features and superior handling.",
                            colors: [
                                {
                                    name: "red",
                                    color: "#90020B",
                                    image: "https://cdn.bajajauto.com/-/media/assets/bajajauto/bikes/pulsar-k-2024/360-images/ebony-black/00.png",
                                    displayName: "Cocktail Wine Red"
                                },
                                {
                                    name: "purple",
                                    color: "#6450D9",
                                    image: "https://cdn.bajajauto.com/-/media/assets/bajajauto/bikes/pulsar-k-2024/360-images/purple-fury/00.png",
                                    displayName: "Purple Fury"
                                },
                                {
                                    name: "green",
                                    color: "#16a34a",
                                    image: "https://cdn.bajajauto.com/-/media/assets/bajajauto/bikes/pulsar-k-2024/360-images/pewter-grey-new/00.png",
                                    displayName: "Racing Green"
                                },
                            ],
                        },

                        {
                            id: "pulsar-150-td",
                            name: "PULSAR 150 TD",
                            image: "./assets/bikes/pulsar/pulsar_150_td.png",
                            category: "Classic",
                            categoryIcon: "./assets/icons/classic.png",
                            description:
                                "The Pulsar 150 TD combines style with efficiency, offering a perfect balance of performance and fuel economy for everyday riding.",
                            colors: [
                                {
                                    name: "black",
                                    color: "#000000",
                                    image: "./assets/bikes/pulsar/pulsar_150_td_black.png",
                                    displayName: "Stealth Black"
                                },
                                {
                                    name: "red",
                                    color: "#dc2626",
                                    image: "./assets/bikes/pulsar/pulsar_150_td_red.png",
                                    displayName: "Passion Red"
                                },
                                {
                                    name: "blue",
                                    color: "#2563eb",
                                    image: "./assets/bikes/pulsar/pulsar_150_td_blue.png",
                                    displayName: "Thunder Blue"
                                },
                            ],
                        },
                        {
                            id: "pulsar-rs200",
                            name: "PULSAR RS200",
                            image: "./assets/bikes/pulsar/pulsar_rs200.png",
                            category: "Sports",
                            categoryIcon: "./assets/icons/sports.png",
                            description:
                                "The Pulsar RS200 is a fully-faired sports bike that combines aggressive styling with high performance for an exhilarating ride.",
                            colors: [
                                {
                                    name: "black",
                                    color: "#000000",
                                    image: "./assets/bikes/pulsar/pulsar_rs200_black.png",
                                    displayName: "Shadow Black"
                                },
                                {
                                    name: "yellow",
                                    color: "#facc15",
                                    image: "./assets/bikes/pulsar/pulsar_rs200_yellow.png",
                                    displayName: "Solar Yellow"
                                },
                                {
                                    name: "red",
                                    color: "#dc2626",
                                    image: "./assets/bikes/pulsar/pulsar_rs200_red.png",
                                    displayName: "Volcanic Red"
                                },
                            ],
                        },
                    ],
                },
                DOMINAR: {
                    logo: "./assets/brand-logos/dominar-logo.svg",
                    bikes: [
                        {
                            id: "dominar-400",
                            name: "DOMINAR 400",
                            image: "./assets/bikes/dominar/dominar_400.png",
                            category: "Adventure",
                            categoryIcon: "./assets/icons/adventure.png",
                            description:
                                "The Dominar 400 is a powerful touring motorcycle designed for long-distance comfort and performance.",
                            colors: [
                                {
                                    name: "black",
                                    color: "#000000",
                                    image: "./assets/bikes/dominar/dominar_400_black.png",
                                    displayName: "Obsidian Black"
                                },
                                {
                                    name: "blue",
                                    color: "#2563eb",
                                    image: "./assets/bikes/dominar/dominar_400_blue.png",
                                    displayName: "Royal Blue"
                                },
                                {
                                    name: "silver",
                                    color: "#6b7280",
                                    image: "./assets/bikes/dominar/dominar_400_silver.png",
                                    displayName: "Chrome Silver"
                                },
                            ],
                        },
                        {
                            id: "dominar-250",
                            name: "DOMINAR 250",
                            image: "./assets/bikes/dominar/dominar_250.png",
                            category: "Touring",
                            categoryIcon: "./assets/icons/touring.png",
                            description:
                                "The Dominar 250 offers a perfect balance of power and efficiency for urban commuting and weekend getaways.",
                            colors: [
                                {
                                    name: "black",
                                    color: "#000000",
                                    image: "./assets/bikes/dominar/dominar_250_black.png",
                                    displayName: "Jet Black"
                                },
                                {
                                    name: "white",
                                    color: "#ffffff",
                                    image: "./assets/bikes/dominar/dominar_250_white.png",
                                    displayName: "Pearl White"
                                },
                                {
                                    name: "red",
                                    color: "#dc2626",
                                    image: "./assets/bikes/dominar/dominar_250_red.png",
                                    displayName: "Scarlet Red"
                                },
                            ],
                        },
                    ],
                },
                AVENGERS: {
                    logo: "./assets/brand-logos/avengers-logo.svg",
                    bikes: [
                        {
                            id: "avenger-220",
                            name: "AVENGER 220",
                            image: "./assets/bikes/avengers/avenger_220.png",
                            category: "Cruiser",
                            categoryIcon: "./assets/icons/cruiser.png",
                            description:
                                "The Avenger 220 combines cruiser comfort with sporty performance for an unmatched riding experience.",
                            colors: [
                                {
                                    name: "black",
                                    color: "#000000",
                                    image: "./assets/bikes/avengers/avenger_220_black.png",
                                    displayName: "Midnight Black"
                                },
                                {
                                    name: "silver",
                                    color: "#6b7280",
                                    image: "./assets/bikes/avengers/avenger_220_silver.png",
                                    displayName: "Metallic Silver"
                                },
                                {
                                    name: "maroon",
                                    color: "#7f1d1d",
                                    image: "./assets/bikes/avengers/avenger_220_maroon.png",
                                    displayName: "Burgundy Red"
                                },
                            ],
                        },
                        {
                            id: "avenger-160",
                            name: "AVENGER 160",
                            image: "./assets/bikes/avengers/avenger_160.png",
                            category: "Cruiser",
                            categoryIcon: "./assets/icons/cruiser.png",
                            description:
                                "The Avenger 160 offers the perfect blend of style and comfort for urban cruising.",
                            colors: [
                                {
                                    name: "black",
                                    color: "#000000",
                                    image: "./assets/bikes/avengers/avenger_160_black.png",
                                    displayName: "Phantom Black"
                                },
                                {
                                    name: "blue",
                                    color: "#2563eb",
                                    image: "./assets/bikes/avengers/avenger_160_blue.png",
                                    displayName: "Ocean Blue"
                                },
                                {
                                    name: "white",
                                    color: "#ffffff",
                                    image: "./assets/bikes/avengers/avenger_160_white.png",
                                    displayName: "Crystal White"
                                },
                            ],
                        },
                    ],
                },
                DISCOVER: {
                    logo: "./assets/brand-logos/discover-logo.svg",
                    bikes: [
                        {
                            id: "discover-125",
                            name: "DISCOVER 125",
                            image: "./assets/bikes/discover/discover_125.png",
                            category: "Commuter",
                            categoryIcon: "./assets/icons/commuter.png",
                            description:
                                "The Discover 125 is designed for efficient urban commuting with its fuel-efficient engine and comfortable riding position.",
                            colors: [
                                {
                                    name: "black",
                                    color: "#000000",
                                    image: "./assets/bikes/discover/discover_125_black.png",
                                    displayName: "Stealth Black"
                                },
                                {
                                    name: "red",
                                    color: "#dc2626",
                                    image: "./assets/bikes/discover/discover_125_red.png",
                                    displayName: "Racing Red"
                                },
                                {
                                    name: "blue",
                                    color: "#2563eb",
                                    image: "./assets/bikes/discover/discover_125_blue.png",
                                    displayName: "Electric Blue"
                                },
                            ],
                        },
                        {
                            id: "discover-110",
                            name: "DISCOVER 110",
                            image: "./assets/bikes/discover/discover_110.png",
                            category: "Commuter",
                            categoryIcon: "./assets/icons/commuter.png",
                            description:
                                "The Discover 110 offers excellent fuel efficiency and low maintenance costs for daily commuting.",
                            colors: [
                                {
                                    name: "black",
                                    color: "#000000",
                                    image: "./assets/bikes/discover/discover_110_black.png",
                                    displayName: "Midnight Black"
                                },
                                {
                                    name: "silver",
                                    color: "#6b7280",
                                    image: "./assets/bikes/discover/discover_110_silver.png",
                                    displayName: "Metallic Silver"
                                },
                                {
                                    name: "green",
                                    color: "#16a34a",
                                    image: "./assets/bikes/discover/discover_110_green.png",
                                    displayName: "Forest Green"
                                },
                            ],
                        },
                    ],
                },
                PLATINA: {
                    logo: "./assets/brand-logos/platina-logo.svg",
                    bikes: [
                        {
                            id: "platina-110",
                            name: "PLATINA 110",
                            image: "./assets/bikes/platina/platina_110.png",
                            category: "Economy",
                            categoryIcon: "./assets/icons/economy.png",
                            description:
                                "The Platina 110 is Bajaj's most fuel-efficient motorcycle, perfect for budget-conscious riders.",
                            colors: [
                                {
                                    name: "black",
                                    color: "#000000",
                                    image: "./assets/bikes/platina/platina_110_black.png",
                                    displayName: "Ebony Black"
                                },
                                {
                                    name: "red",
                                    color: "#dc2626",
                                    image: "./assets/bikes/platina/platina_110_red.png",
                                    displayName: "Racing Red"
                                },
                                {
                                    name: "silver",
                                    color: "#6b7280",
                                    image: "./assets/bikes/platina/platina_110_silver.png",
                                    displayName: "Platinum Silver"
                                },
                            ],
                        },
                        {
                            id: "platina-100",
                            name: "PLATINA 100",
                            image: "./assets/bikes/platina/platina_100.png",
                            category: "Economy",
                            categoryIcon: "./assets/icons/economy.png",
                            description:
                                "The Platina 100 offers unbeatable fuel efficiency and low maintenance costs for daily commuting.",
                            colors: [
                                {
                                    name: "black",
                                    color: "#000000",
                                    image: "./assets/bikes/platina/platina_100_black.png",
                                    displayName: "Carbon Black"
                                },
                                {
                                    name: "blue",
                                    color: "#2563eb",
                                    image: "./assets/bikes/platina/platina_100_blue.png",
                                    displayName: "Ocean Blue"
                                },
                                {
                                    name: "white",
                                    color: "#ffffff",
                                    image: "./assets/bikes/platina/platina_100_white.png",
                                    displayName: "Pearl White"
                                },
                            ],
                        },
                    ],
                },
            };

            // Bike carousel state
            let currentBrand = "PULSAR";
            let currentBikeIndex = 0;
            let currentColorIndex = 0;

            // DOM elements
            const backgroundText = document.getElementById("background-brand-text");
            const bikeTitle = document.getElementById("bike-title");
            const bikeDescription = document.getElementById("bike-description");
            const mainBikeImage = document.getElementById("main-bike-image");
            const brandLogo = document.getElementById("brand-logo");
            const categoryIcon = document.getElementById("category-icon");
            const categoryText = document.getElementById("category-text");
            const colorSelection = document.getElementById("color-selection");
            const brandTabsContainer = document.getElementById("brand-tabs-container");
            const modelTabsContainer = document.getElementById("model-tabs-container");
            const prevBtn = document.getElementById("bike-prev-btn");
            const nextBtn = document.getElementById("bike-next-btn");

            console.log("DOM elements found:", {
                backgroundText: !!backgroundText,
                bikeTitle: !!bikeTitle,
                mainBikeImage: !!mainBikeImage,
                brandTabsContainer: !!brandTabsContainer,
                modelTabsContainer: !!modelTabsContainer,
            });

            // Function to generate brand tabs dynamically
            function generateBrandTabs() {
                if (!brandTabsContainer) return;

                brandTabsContainer.innerHTML = "";
                const brands = Object.keys(bikeData);

                brands.forEach((brand, index) => {
                    const brandTab = document.createElement("button");
                    brandTab.className = `brand-tab text-lg lg:text-xl font-semibold border-b-2 pb-2 ${index === 0
                        ? "active text-gray-800 border-black"
                        : "text-gray-400 border-transparent hover:text-gray-600"
                        }`;
                    brandTab.dataset.brand = brand;
                    brandTab.textContent = brand;

                    brandTab.addEventListener("click", () => {
                        switchBrand(brand);
                    });

                    brandTabsContainer.appendChild(brandTab);
                });
            }

            // Function to generate model tabs dynamically
            function generateModelTabs() {
                if (!modelTabsContainer) return;

                modelTabsContainer.innerHTML = "";
                const brandData = bikeData[currentBrand];
                const modelGroup = document.createElement("div");
                modelGroup.className = `model-group ${currentBrand.toLowerCase()}-models`;
                const modelButtonsContainer = document.createElement("div");
                modelButtonsContainer.className = "inline-flex flex-wrap gap-2 border-t border-gray-200 px-4";
                brandData.bikes.forEach((bike, index) => {
                    const modelBtn = document.createElement("button");
                    modelBtn.className = `variant-btn px-4 py-2 text-sm font-medium border-t-2 ${index === currentBikeIndex ? "active text-gray-700 border-black" : "text-gray-500 border-transparent"}`;
                    modelBtn.dataset.model = bike.id;
                    modelBtn.dataset.image = bike.image;
                    modelBtn.dataset.name = bike.name;
                    modelBtn.dataset.description = bike.description;
                    modelBtn.textContent = bike.name;
                    modelBtn.addEventListener("click", () => {
                        currentBikeIndex = index;
                        currentColorIndex = 0;
                        updateDisplay();
                    });
                    modelButtonsContainer.appendChild(modelBtn);
                });
                modelGroup.appendChild(modelButtonsContainer);
                modelTabsContainer.innerHTML = "";
                modelTabsContainer.appendChild(modelGroup);
            }

            // Helper function to get color style
            function getColorStyle(colorName) {
                const colorMap = {
                    black: "#000000",
                    white: "#ffffff",
                    red: "#dc2626",
                    blue: "#2563eb",
                    yellow: "#facc15",
                    green: "#16a34a",
                    orange: "#ea580c",
                    silver: "#6b7280",
                    maroon: "#7f1d1d",
                };
                return colorMap[colorName] || "#000000";
            }

            // Update display function
            function updateDisplay() {
                const brandData = bikeData[currentBrand];
                const currentBike = brandData.bikes[currentBikeIndex];
                const currentColor = currentBike.colors[currentColorIndex];

                console.log("Updating display:", {
                    brand: currentBrand,
                    bikeIndex: currentBikeIndex,
                    colorIndex: currentColorIndex,
                    bike: currentBike.name,
                });

                // Update background text
                if (backgroundText) {
                    backgroundText.textContent = currentBrand;

                    // Toggle pulsar-text class based on brand
                    if (currentBrand === "PULSAR") {
                        backgroundText.classList.add("pulsar-text");
                        backgroundText.classList.remove("other-brand-text");
                    } else {
                        backgroundText.classList.remove("pulsar-text");
                        backgroundText.classList.add("other-brand-text");
                    }
                }

                // Update bike title and description
                if (bikeTitle) {
                    bikeTitle.textContent = currentBike.name;
                }
                if (bikeDescription) {
                    bikeDescription.textContent = currentBike.description;
                }

                // Update main bike image
                if (mainBikeImage) {
                    // Use the current selected color's image, fallback to first color, then default bike image
                    const currentColor = currentBike.colors && currentBike.colors.length > 0 ? currentBike.colors[currentColorIndex] : null;
                    const firstColor = currentBike.colors && currentBike.colors.length > 0 ? currentBike.colors[0] : null;
                    const imageToUse = currentColor && currentColor.image ? currentColor.image :
                        (firstColor && firstColor.image ? firstColor.image : currentBike.image);
                    mainBikeImage.src = imageToUse;
                    mainBikeImage.alt = currentBike.name;
                }

                // Update brand logo and category
                if (brandLogo) {
                    brandLogo.src = brandData.logo;
                    brandLogo.alt = currentBrand + " Logo";
                }
                if (categoryIcon) {
                    categoryIcon.src = currentBike.categoryIcon;
                    categoryIcon.alt = currentBike.category + " Category";
                }
                if (categoryText) {
                    categoryText.textContent = `${capitalizeFirstLetter(currentBrand)} ${currentBike.category}`;
                }

                // Update color selection with custom names and images
                if (colorSelection) {
                    // Clear existing content
                    colorSelection.innerHTML = "";

                    // Create color name display
                    const colorNameDisplay = document.createElement('div');
                    colorNameDisplay.className = 'text-center';
                    const currentColor = currentBike.colors && currentBike.colors.length > 0 ? currentBike.colors[currentColorIndex] : null;
                    colorNameDisplay.innerHTML = `
            <div class="font-medium text-gray-900" id="current-color-name">${currentColor ? currentColor.displayName : 'Black'}</div>
          `;
                    colorSelection.appendChild(colorNameDisplay);

                    // Create color buttons container
                    const colorButtonsContainer = document.createElement('div');
                    colorButtonsContainer.className = 'flex space-x-2';

                    currentBike.colors.forEach((color, index) => {
                        const colorBtn = document.createElement("button");
                        colorBtn.className = `color-btn w-8 h-8 rounded-full border-2 border-[#326AD2] ${index === currentColorIndex ? "active" : ""}`;
                        colorBtn.style.backgroundColor = color.color;
                        colorBtn.dataset.color = color.name;
                        colorBtn.dataset.colorName = color.displayName;
                        colorBtn.dataset.image = color.image;
                        colorBtn.title = color.displayName; // Show color name on hover

                        colorBtn.addEventListener("click", () => {
                            currentColorIndex = index;
                            updateDisplay();
                        });

                        colorButtonsContainer.appendChild(colorBtn);
                    });

                    colorSelection.appendChild(colorButtonsContainer);
                }

                // Update brand tabs
                brandTabsContainer.querySelectorAll(".brand-tab").forEach((tab) => {
                    if (tab.dataset.brand === currentBrand) {
                        tab.classList.add("active");
                        tab.classList.remove("text-gray-400");
                        tab.classList.add("text-gray-800", "border-[#326AD2]");
                        tab.classList.remove("border-transparent");
                    } else {
                        tab.classList.remove("active");
                        tab.classList.add("text-gray-400");
                        tab.classList.remove("text-gray-800", "border-[#326AD2]");
                        tab.classList.add("border-transparent");
                    }
                });

                // Update model tabs
                modelTabsContainer.querySelectorAll(".model-group").forEach((group) => {
                    if (group.classList.contains(currentBrand.toLowerCase() + "-models")) {
                        group.classList.remove("hidden");
                        const buttons = group.querySelectorAll(".variant-btn");
                        buttons.forEach((btn, index) => {
                            if (index === currentBikeIndex) {
                                btn.classList.add("active");
                                btn.classList.remove("text-gray-500", "border-transparent");
                                btn.classList.add("text-gray-700", "border-black");
                            } else {
                                btn.classList.remove("active");
                                btn.classList.add("text-gray-500", "border-transparent");
                                btn.classList.remove("text-gray-700", "border-black");
                            }
                        });
                    } else {
                        group.classList.add("hidden");
                    }
                });
            }

            // Navigation functions
            function nextBike() {
                const brandData = bikeData[currentBrand];
                currentBikeIndex = (currentBikeIndex + 1) % brandData.bikes.length;
                currentColorIndex = 0; // Reset color when changing bike
                updateDisplay();
            }

            function prevBike() {
                const brandData = bikeData[currentBrand];
                currentBikeIndex =
                    (currentBikeIndex - 1 + brandData.bikes.length) %
                    brandData.bikes.length;
                currentColorIndex = 0; // Reset color when changing bike
                updateDisplay();
            }

            function switchBrand(brand) {
                if (bikeData[brand]) {
                    currentBrand = brand;
                    currentBikeIndex = 0;
                    currentColorIndex = 0;
                    updateDisplay();
                }
            }

            function capitalizeFirstLetter(str) {
                if (!str) return "";
                return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
            }

            // ========================================
            // LARAVEL INTEGRATION SETUP
            // ========================================

            // Make bike data available globally for the carousel
            window.bikeData = bikeData;

            console.log("✅ Bike data loaded and available globally");
            console.log("📊 Brands available:", Object.keys(bikeData));

            // Generate tabs dynamically
            generateBrandTabs();
            generateModelTabs();

            // Add event listeners for navigation buttons
            if (prevBtn) {
                prevBtn.addEventListener("click", prevBike);
            }
            if (nextBtn) {
                nextBtn.addEventListener("click", nextBike);
            }

            // Initialize the display
            updateDisplay();
        });
    </script>

</body>

</html>