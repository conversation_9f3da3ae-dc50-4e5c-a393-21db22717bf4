<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bajaj Motorcycles - Enhanced Mega Menu</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'bajaj-blue': '#0047AB',
                        'bajaj-red': '#E31937',
                    }
                }
            }
        }
    </script>
    <style>
        .model-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 1rem;
        }
        .category-btn.active {
            background-color: white;
            color: #0047AB;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .tab-btn.active {
            color: #0047AB;
            border-bottom: 2px solid #0047AB;
        }
        .mobile-model-item:hover {
            background-color: #f9fafb;
        }
        .scrollbar-hide::-webkit-scrollbar {
            display: none;
        }
        .scrollbar-hide {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        .media-dropdown {
            min-width: 200px;
        }
        /* Fixed overlay stacking context */
        #mobile-overlay {
            z-index: 40; /* Below category detail */
        }
        #mobile-category-detail {
            z-index: 60; /* Above overlay */
        }
        /* Improved mobile navigation */
        .mobile-nav-section {
            transition: all 0.3s ease;
        }
        .category-chip {
            border: 1px solid #e5e7eb;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.875rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .category-chip.active {
            background-color: #0047AB;
            color: white;
            border-color: #0047AB;
        }
        /* Mega Menu Fixed Height */
        .mega-menu-container {
            height: 500px; /* Fixed height */
            overflow: hidden; /* Hide scrollbars for the container */
        }
        .mega-menu-scrollable {
            height: 100%;
            overflow-y: auto; /* Enable scrolling for content */
            scrollbar-width: thin;
            scrollbar-color: #0047AB #f1f1f1;
        }
        .mega-menu-scrollable::-webkit-scrollbar {
            width: 8px;
        }
        .mega-menu-scrollable::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }
        .mega-menu-scrollable::-webkit-scrollbar-thumb {
            background: #0047AB;
            border-radius: 4px;
        }
        .mega-menu-scrollable::-webkit-scrollbar-thumb:hover {
            background: #003380;
        }
        .categories-sidebar {
            height: 100%;
            overflow-y: auto;
        }
        .models-section {
            height: calc(100% - 50px); /* Account for tabs height */
        }
        /* Category heading styles */
        .category-heading {
            grid-column: 1 / -1;
            text-align: left;
            padding: 10px 0 5px;
            font-weight: 600;
            color: #0047AB;
            margin-top: 15px;
            border-bottom: 1px solid #e5e7eb;
        }
        .category-heading:first-child {
            margin-top: 0;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Top Bar -->
    <div class="bg-gray-800 text-white text-xs py-1">
        <div class="container mx-auto px-4 flex justify-between items-center">
            <span>GOLDENIA GROUP WITH LEGACY OF 100 YEARS</span>
            <span>International website</span>
        </div>
    </div>

    <!-- Main Navbar -->
    <nav class="bg-white shadow-md relative">
        <div class="container mx-auto px-4">
            <div class="flex justify-between items-center h-16">
                <!-- Mobile Menu Button -->
                <button id="mobile-menu-btn" class="lg:hidden p-2">
                    <i class="fas fa-bars text-xl text-gray-700"></i>
                </button>

                <!-- Logo -->
                <div class="flex items-center">
                    <div class="bg-bajaj-blue text-white px-3 py-1 rounded text-sm font-bold">
                        BAJAJ
                    </div>
                    <div class="ml-2 text-bajaj-blue text-sm font-semibold">
                        COME AS YOU ARE
                    </div>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden lg:flex items-center space-x-8">
                    <!-- Motorcycles Dropdown -->
                    <div class="relative group">
                        <button class="flex items-center space-x-1 text-gray-700 hover:text-bajaj-blue font-medium">
                            <span>MOTORCYCLES</span>
                            <i class="fas fa-chevron-down text-xs"></i>
                        </button>

                        <!-- Mega Dropdown - Fixed Height Container -->
                        <div class="absolute top-full left-0 w-screen max-w-5xl bg-white shadow-lg border-t-2 border-bajaj-blue opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50 mega-menu-container">
                            <div class="flex h-full">
                                <!-- Categories Sidebar - Scrollable -->
                                <div class="w-48 bg-gray-100 p-4 categories-sidebar">
                                    <div class="space-y-2">
                                        <button class="category-btn w-full text-left px-3 py-2 text-sm font-medium text-gray-700 hover:bg-white hover:text-bajaj-blue rounded active" data-category="pulsar">
                                            PULSAR
                                        </button>
                                        <button class="category-btn w-full text-left px-3 py-2 text-sm font-medium text-gray-700 hover:bg-white hover:text-bajaj-blue rounded" data-category="dominar">
                                            DOMINAR
                                        </button>
                                        <button class="category-btn w-full text-left px-3 py-2 text-sm font-medium text-gray-700 hover:bg-white hover:text-bajaj-blue rounded" data-category="avengers">
                                            AVENGERS
                                        </button>
                                        <button class="category-btn w-full text-left px-3 py-2 text-sm font-medium text-gray-700 hover:bg-white hover:text-bajaj-blue rounded" data-category="discover">
                                            DISCOVER
                                        </button>
                                        <button class="category-btn w-full text-left px-3 py-2 text-sm font-medium text-gray-700 hover:bg-white hover:text-bajaj-blue rounded" data-category="platina">
                                            PLATINA
                                        </button>
                                    </div>
                                </div>

                                <!-- Models Section - Scrollable -->
                                <div class="mega-menu-scrollable flex-1 flex flex-col">
                                    <div class="p-6">
                                        <!-- Category Tabs -->
                                        <div id="tabs-container" class="flex space-x-6 mb-4 text-sm">
                                            <!-- Tabs will be dynamically generated -->
                                        </div>

                                        <!-- Models Content -->
                                        <div id="models-content" class="model-grid gap-4 models-section">
                                            <!-- Models will be populated by JavaScript -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <a href="#" class="text-gray-700 hover:text-bajaj-blue font-medium">SHOWROOMS</a>
                    <a href="#" class="text-gray-700 hover:text-bajaj-blue font-medium">WORKSHOPS</a>
                    <a href="#" class="text-gray-700 hover:text-bajaj-blue font-medium">EVENTS</a>
                    <a href="#" class="bg-bajaj-blue text-white px-4 py-2 rounded font-medium hover:bg-blue-700">BOOK TEST RIDE</a>
                    <a href="/about.html" class="text-gray-700 hover:text-bajaj-blue font-medium">ABOUT US</a>
                    <a href="#" class="text-gray-700 hover:text-bajaj-blue font-medium">NEWS</a>
                    
                    <!-- Media Center Dropdown - Desktop -->
                    <div class="relative group">
                        <button class="flex items-center space-x-1 text-gray-700 hover:text-bajaj-blue font-medium">
                            <span>MEDIA CENTER</span>
                            <i class="fas fa-chevron-down text-xs"></i>
                        </button>
                        <div class="absolute top-full left-0 w-48 bg-white shadow-lg rounded-md opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50 media-dropdown">
                            <div class="py-2">
                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-bajaj-blue">ABOUT US</a>
                                <a href="/blogs.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-bajaj-blue">ANNOUNCEMENTS</a>
                                <a href="/blogs.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-bajaj-blue">EVENTS</a>
                                <a href="/blogs.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-bajaj-blue">BLOGS</a>
                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-bajaj-blue">DOWNLOAD CENTER</a>
                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-bajaj-blue">CONTACT US</a>
                                <a href="faqs.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-bajaj-blue">FAQS</a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Mobile BIKES Button -->
                <div class="lg:hidden">
                    <span class="text-gray-700 font-medium">BIKES</span>
                </div>
            </div>
        </div>

        <!-- Mobile Menu Overlay - Fixed z-index -->
        <div id="mobile-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 hidden"></div>

        <!-- Mobile Menu -->
        <div id="mobile-menu" class="mobile-nav-section fixed top-0 left-0 w-80 h-full bg-white z-50 transform -translate-x-full transition-transform duration-300">
            <div class="p-4">
                <button id="close-mobile-menu" class="absolute top-4 right-4 text-xl text-gray-600">
                    <i class="fas fa-times"></i>
                </button>

                <!-- Mobile Menu Items -->
                <div class="mt-8 space-y-4">
                    <div class="mobile-dropdown">
                        <button class="mobile-dropdown-btn flex items-center justify-between w-full text-left font-medium text-gray-700 py-2">
                            <span>BIKES</span>
                            <i class="fas fa-chevron-right text-xs"></i>
                        </button>
                        <div class="mobile-dropdown-content hidden pl-4 space-y-2 mt-2">
                            <button class="mobile-category-btn block w-full text-left py-2 text-sm text-gray-600" data-category="pulsar">PULSAR</button>
                            <button class="mobile-category-btn block w-full text-left py-2 text-sm text-gray-600" data-category="dominar">DOMINAR</button>
                            <button class="mobile-category-btn block w-full text-left py-2 text-sm text-gray-600" data-category="avengers">AVENGERS</button>
                            <button class="mobile-category-btn block w-full text-left py-2 text-sm text-gray-600" data-category="discover">DISCOVER</button>
                            <button class="mobile-category-btn block w-full text-left py-2 text-sm text-gray-600" data-category="platina">PLATINA</button>
                        </div>
                    </div>

                    <a href="#" class="block py-2 font-medium text-gray-700">SHOWROOMS</a>
                    <a href="#" class="block py-2 font-medium text-gray-700">WORKSHOPS</a>
                    <a href="#" class="block py-2 font-medium text-gray-700">EVENTS</a>
                    <a href="#" class="block py-2 font-medium text-gray-700">BOOK TEST RIDE</a>
                    <a href="#" class="block py-2 font-medium text-gray-700">ABOUT US</a>
                    <a href="#" class="block py-2 font-medium text-gray-700">NEWS</a>

                    <!-- Media Center Dropdown - Mobile -->
                    <div class="mobile-dropdown">
                        <button class="mobile-dropdown-btn flex items-center justify-between w-full text-left font-medium text-gray-700 py-2">
                            <span>MEDIA CENTER</span>
                            <i class="fas fa-chevron-right text-xs"></i>
                        </button>
                        <div class="mobile-dropdown-content hidden pl-4 space-y-2 mt-2">
                            <a href="#" class="block py-2 text-sm text-gray-600">ABOUT US</a>
                            <a href="#" class="block py-2 text-sm text-gray-600">ANNOUNCEMENTS</a>
                            <a href="#" class="block py-2 text-sm text-gray-600">EVENTS</a>
                            <a href="#" class="block py-2 text-sm text-gray-600">BLOGS</a>
                            <a href="#" class="block py-2 text-sm text-gray-600">DOWNLOAD CENTER</a>
                            <a href="#" class="block py-2 text-sm text-gray-600">CONTACT US</a>
                            <a href="#" class="block py-2 text-sm text-gray-600">FAQS</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Mobile Category Detail View - Fixed z-index -->
        <div id="mobile-category-detail" class="mobile-nav-section fixed top-0 left-0 w-80 h-full bg-white z-60 transform -translate-x-full transition-transform duration-300">
            <div class="p-4">
                <div class="flex items-center mb-4">
                    <button id="back-to-categories" class="mr-3 text-gray-600">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <button id="close-category-detail" class="absolute top-4 right-4 text-xl text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                    <span id="category-title" class="font-medium text-gray-800">BIKES</span>
                </div>
                
                <!-- Category Switcher -->
                <div class="mb-6">
                    <h3 class="text-sm font-semibold text-gray-700 mb-2">Switch Category:</h3>
                    <div class="flex flex-wrap gap-2" id="mobile-category-switcher">
                        <!-- Categories will be populated by JavaScript -->
                    </div>
                </div>

                <!-- Category Tabs -->
                <div id="mobile-tabs-container" class="flex space-x-4 mb-4 text-sm border-b pb-2">
                    <!-- Tabs will be dynamically generated -->
                </div>

                <!-- Mobile Models List -->
                <div id="mobile-models-list" class="space-y-3 max-h-[calc(100vh-200px)] overflow-y-auto scrollbar-hide">
                    <!-- Models will be populated by JavaScript -->
                </div>
            </div>
        </div>
    </nav>

    <script>
        // Comprehensive motorcycle data with brand-specific categories
        const motorcycleData = {
            pulsar: {
                classic: [
                    { name: "PULSAR 220F ABS", image: "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=P220F" },
                    { name: "PULSAR 150 TD", image: "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=P150TD" },
                    { name: "PULSAR 150", image: "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=P150" },
                    { name: "PULSAR 125", image: "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=P125" },
                ],
                ns: [
                    { name: "PULSAR NS400Z", image: "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=NS400Z" },
                    { name: "PULSAR NS 200 ABS FI", image: "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=NS200FI" },
                    { name: "PULSAR NS 200 ABS", image: "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=NS200" },
                    { name: "PULSAR NS 200", image: "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=NS200" },
                    { name: "PULSAR NS 160 ABS", image: "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=NS160" },
                    { name: "PULSAR NS160 FI DUAL ABS BS6", image: "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=NS160BS6" },
                    { name: "PULSAR NS 125 BS6", image: "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=NS125BS6" },
                    { name: "PULSAR NS 125 FI BS6", image: "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=NS125FIB" },
                ],
                n: [
                    { name: "PULSAR N250", image: "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=N250" },
                    { name: "PULSAR N160", image: "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=N160" },
                ]
            },
            dominar: {
                classic: [
                    { name: "DOMINAR 400", image: "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=D400" },
                    { name: "DOMINAR 250", image: "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=D250" },
                ]
            },
            avengers: {
                cruiser: [
                    { name: "AVENGER CRUISE 220", image: "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=AV220" },
                    { name: "AVENGER STREET 160", image: "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=AV160" },
                ]
            },
            discover: {
                commuter: [
                    { name: "DISCOVER 125", image: "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=D125" },
                    { name: "DISCOVER 110", image: "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=D110" },
                ]
            },
            platina: {
                commuter: [
                    { name: "PLATINA 110", image: "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=PL110" },
                    { name: "PLATINA 100", image: "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=PL100" },
                ]
            }
        };
        
        let currentCategory = 'pulsar';
        let currentTab = 'all';
        
        // DOM elements
        const mobileOverlay = document.getElementById('mobile-overlay');
        const mobileMenu = document.getElementById('mobile-menu');
        const mobileCategoryDetail = document.getElementById('mobile-category-detail');
        const mobileMenuBtn = document.getElementById('mobile-menu-btn');
        const closeMobileMenu = document.getElementById('close-mobile-menu');
        const backToCategories = document.getElementById('back-to-categories');
        const closeCategoryDetail = document.getElementById('close-category-detail');
        const mobileCategorySwitcher = document.getElementById('mobile-category-switcher');
        
        // Function to format sub-category names
        function formatSubCategoryName(name) {
            if (name === 'ns') return 'NS';
            if (name === 'n') return 'N';
            if (name === 'classic') return 'Classic';
            if (name === 'cruiser') return 'Cruiser';
            if (name === 'commuter') return 'Commuter';
            return name.charAt(0).toUpperCase() + name.slice(1);
        }
        
        // Function to render tabs dynamically
        function renderTabs(category, isMobile = false) {
            const tabsContainer = isMobile ? 
                document.getElementById('mobile-tabs-container') : 
                document.getElementById('tabs-container');
                
            tabsContainer.innerHTML = '';
            
            // Get available tabs for this category
            const availableTabs = Object.keys(motorcycleData[category])
                .filter(tab => motorcycleData[category][tab].length > 0);
            
            // Always show "All" tab
            const allTab = document.createElement('button');
            allTab.className = `tab-btn ${currentTab === 'all' ? 'text-bajaj-blue border-b-2 border-bajaj-blue active' : 'text-gray-500'} pb-1`;
            allTab.dataset.tab = 'all';
            allTab.textContent = 'All';
            tabsContainer.appendChild(allTab);
            
            // Create tabs for each available category
            availableTabs.forEach(tabName => {
                const tab = document.createElement('button');
                tab.className = `tab-btn ${currentTab === tabName ? 'text-bajaj-blue border-b-2 border-bajaj-blue active' : 'text-gray-500'} pb-1`;
                tab.dataset.tab = tabName;
                tab.textContent = formatSubCategoryName(tabName);
                tabsContainer.appendChild(tab);
            });
            
            // Add event listeners to new tabs
            addTabEventListeners(isMobile);
        }
        
        // Function to render models
        function renderModels(category, tab, isMobile = false) {
            const container = isMobile ? 
                document.getElementById('mobile-models-list') : 
                document.getElementById('models-content');
                
            let models = [];
            
            if (tab === 'all') {
                // Create container HTML with category headings
                let containerHTML = '';
                
                // Get all sub-categories for this category
                const subCategories = Object.keys(motorcycleData[category]);
                
                // Iterate through each sub-category
                for (const subCategory of subCategories) {
                    const subCategoryModels = motorcycleData[category][subCategory] || [];
                    if (subCategoryModels.length === 0) continue;
                    
                    // Add category heading
                    if (isMobile) {
                        containerHTML += `
                            <div class="mt-4 mb-2 pl-3">
                                <div class="text-sm font-semibold text-bajaj-blue border-b border-gray-200 pb-1">
                                    ${formatSubCategoryName(subCategory)}
                                </div>
                            </div>
                        `;
                    } else {
                        containerHTML += `
                            <div class="category-heading">
                                ${formatSubCategoryName(subCategory)}
                            </div>
                        `;
                    }
                    
                    // Add models for this sub-category
                    subCategoryModels.forEach(model => {
                        if (isMobile) {
                            containerHTML += `
                                <div class="mobile-model-item flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg cursor-pointer">
                                    <img src="${model.image}" alt="${model.name}" class="w-16 h-10 object-cover rounded">
                                    <div class="flex-1">
                                        <div class="text-sm font-medium text-gray-800">${model.name}</div>
                                    </div>
                                    <i class="fas fa-chevron-right text-gray-400"></i>
                                </div>
                            `;
                        } else {
                            containerHTML += `
                                <div class="model-item text-center p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors">
                                    <img src="${model.image}" alt="${model.name}" class="mx-auto mb-2 w-24 h-16 object-cover rounded">
                                    <p class="text-xs font-medium text-gray-800 mb-1">${model.name}</p>
                                </div>
                            `;
                        }
                    });
                }
                
                container.innerHTML = containerHTML;
            } else {
                // For specific tabs, just show models without headings
                models = motorcycleData[category][tab] || [];
                
                if (isMobile) {
                    container.innerHTML = models.map(model => `
                        <div class="mobile-model-item flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg cursor-pointer">
                            <img src="${model.image}" alt="${model.name}" class="w-16 h-10 object-cover rounded">
                            <div class="flex-1">
                                <div class="text-sm font-medium text-gray-800">${model.name}</div>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>
                    `).join('');
                } else {
                    container.innerHTML = models.map(model => `
                        <div class="model-item text-center p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors">
                            <img src="${model.image}" alt="${model.name}" class="mx-auto mb-2 w-24 h-16 object-cover rounded">
                            <p class="text-xs font-medium text-gray-800 mb-1">${model.name}</p>
                        </div>
                    `).join('');
                }
                
                // Show message if no models found
                if (models.length === 0) {
                    container.innerHTML = `
                        <div class="${isMobile ? 'text-center py-8 text-gray-500' : 'col-span-full text-center py-8 text-gray-500'}">
                            No models available in this category
                        </div>
                    `;
                }
            }
        }
        
        // Function to render category switcher
        function renderCategorySwitcher(currentCategory) {
            mobileCategorySwitcher.innerHTML = '';
            
            const categories = ['pulsar', 'dominar', 'avengers', 'discover', 'platina'];
            
            categories.forEach(category => {
                const chip = document.createElement('div');
                chip.className = `category-chip ${currentCategory === category ? 'active bg-bajaj-blue text-white' : 'bg-gray-100 text-gray-700'}`;
                chip.textContent = category.toUpperCase();
                chip.dataset.category = category;
                
                chip.addEventListener('click', () => {
                    // Remove active class from all chips
                    document.querySelectorAll('.category-chip').forEach(c => {
                        c.classList.remove('active', 'bg-bajaj-blue', 'text-white');
                        c.classList.add('bg-gray-100', 'text-gray-700');
                    });
                    
                    // Add active class to clicked chip
                    chip.classList.remove('bg-gray-100', 'text-gray-700');
                    chip.classList.add('active', 'bg-bajaj-blue', 'text-white');
                    
                    // Update current category
                    currentCategory = category;
                    
                    // Update category title
                    document.getElementById('category-title').textContent = `BIKES / ${category.toUpperCase()}`;
                    
                    // Render new tabs and models
                    currentTab = 'all';
                    renderTabs(category, true);
                    renderModels(category, 'all', true);
                });
                
                mobileCategorySwitcher.appendChild(chip);
            });
        }
        
        // Initialize with default category and tab
        renderTabs(currentCategory);
        renderModels(currentCategory, currentTab);
        
        // Add tab event listeners
        function addTabEventListeners(isMobile = false) {
            const selector = isMobile ? '#mobile-tabs-container .tab-btn' : '#tabs-container .tab-btn';
            const tabs = document.querySelectorAll(selector);
            
            tabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    const tabName = tab.dataset.tab;
                    currentTab = tabName;
                    
                    // Update active tab styling
                    tabs.forEach(t => {
                        t.classList.remove('text-bajaj-blue', 'border-b-2', 'border-bajaj-blue', 'active');
                        t.classList.add('text-gray-500');
                    });
                    tab.classList.remove('text-gray-500');
                    tab.classList.add('text-bajaj-blue', 'border-b-2', 'border-bajaj-blue', 'active');
                    
                    renderModels(currentCategory, tabName, isMobile);
                });
            });
        }
        
        // Mobile menu functionality
        // Open mobile menu
        mobileMenuBtn.addEventListener('click', () => {
            mobileMenu.classList.remove('-translate-x-full');
            mobileOverlay.classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        });
        
        // Close mobile menu
        function closeMobileMenuFunc() {
            mobileMenu.classList.add('-translate-x-full');
            mobileCategoryDetail.classList.add('-translate-x-full');
            mobileOverlay.classList.add('hidden');
            document.body.style.overflow = 'auto';
        }
        
        closeMobileMenu.addEventListener('click', closeMobileMenuFunc);
        mobileOverlay.addEventListener('click', closeMobileMenuFunc);
        
        // Mobile dropdown functionality
        document.querySelectorAll('.mobile-dropdown-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const content = btn.nextElementSibling;
                const icon = btn.querySelector('i');
                
                if (content.classList.contains('hidden')) {
                    content.classList.remove('hidden');
                    icon.classList.remove('fa-chevron-right');
                    icon.classList.add('fa-chevron-down');
                } else {
                    content.classList.add('hidden');
                    icon.classList.remove('fa-chevron-down');
                    icon.classList.add('fa-chevron-right');
                }
            });
        });
        
        // Mobile category selection
        document.querySelectorAll('.mobile-category-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const category = btn.dataset.category;
                currentCategory = category;
                currentTab = 'all';
                
                // Update category title
                document.getElementById('category-title').textContent = `BIKES / ${category.toUpperCase()}`;
                
                // Render category switcher
                renderCategorySwitcher(category);
                
                // Render tabs and models for mobile
                renderTabs(category, true);
                renderModels(category, 'all', true);
                
                // Hide overlay when showing model list
                mobileOverlay.classList.add('hidden');
                
                // Transition to model view
                mobileMenu.classList.add('-translate-x-full');
                setTimeout(() => {
                    mobileCategoryDetail.classList.remove('-translate-x-full');
                }, 50);
            });
        });
        
        // Back to categories
        backToCategories.addEventListener('click', () => {
            mobileCategoryDetail.classList.add('-translate-x-full');
            setTimeout(() => {
                mobileMenu.classList.remove('-translate-x-full');
                mobileOverlay.classList.remove('hidden');
            }, 50);
        });
        
        // Close category detail
        closeCategoryDetail.addEventListener('click', closeMobileMenuFunc);
        
        // Desktop category switching
        document.querySelectorAll('.category-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const category = btn.dataset.category;
                currentCategory = category;
                currentTab = 'all';
                
                // Remove active class from all buttons
                document.querySelectorAll('.category-btn').forEach(b => {
                    b.classList.remove('active', 'bg-white', 'text-bajaj-blue');
                    b.classList.add('text-gray-700');
                });
                
                // Add active class to clicked button
                btn.classList.remove('text-gray-700');
                btn.classList.add('active', 'bg-white', 'text-bajaj-blue');
                
                // Render new tabs and models
                renderTabs(category);
                renderModels(category, 'all');
            });
        });
        
        // Add click handlers for model items
        document.addEventListener('click', (e) => {
            if (e.target.closest('.model-item') || e.target.closest('.mobile-model-item')) {
                const modelElement = e.target.closest('.model-item, .mobile-model-item');
                const modelName = modelElement.querySelector('p, .text-sm').textContent;
                alert(`You selected: ${modelName}\n\nThis would typically navigate to the model details page.`);
            }
        });
        
        // Initialize tabs with event listeners
        addTabEventListeners();
    </script>
</body>
</html>